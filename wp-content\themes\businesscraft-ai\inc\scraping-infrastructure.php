<?php
/**
 * Advanced Scraping Infrastructure Components
 * 
 * Supporting classes for user agent management, proxy rotation,
 * session handling, rate limiting, and performance monitoring.
 *
 * @package ChatGABI
 * @since 1.3.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * User Agent Manager for Anti-Bot Detection
 */
class ChatGABI_User_Agent_Manager {
    
    private $user_agents;
    private $rotation_index;
    
    public function __construct() {
        $this->load_user_agents();
        $this->rotation_index = 0;
    }
    
    /**
     * Load diverse user agent strings
     */
    private function load_user_agents() {
        $this->user_agents = array(
            // Chrome on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            
            // Chrome on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            
            // Firefox on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            
            // Safari on macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            
            // Edge on Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            
            // Mobile browsers
            'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
        );
    }
    
    /**
     * Get random user agent
     */
    public function get_random_agent() {
        return $this->user_agents[array_rand($this->user_agents)];
    }
    
    /**
     * Get rotating user agent
     */
    public function get_rotating_agent() {
        $agent = $this->user_agents[$this->rotation_index];
        $this->rotation_index = ($this->rotation_index + 1) % count($this->user_agents);
        return $agent;
    }
}

/**
 * Proxy Manager for IP Rotation
 */
class ChatGABI_Proxy_Manager {
    
    private $proxy_list;
    private $proxy_rotation;
    private $proxy_health;
    
    public function __construct() {
        $this->load_proxy_configuration();
        $this->proxy_rotation = array();
        $this->proxy_health = array();
    }
    
    /**
     * Load proxy configuration
     */
    private function load_proxy_configuration() {
        // Load from WordPress options or configuration file
        $this->proxy_list = get_option('chatgabi_proxy_list', array());
        
        // Default to no proxies if none configured
        if (empty($this->proxy_list)) {
            $this->proxy_list = array();
        }
    }
    
    /**
     * Get proxy for specific country
     */
    public function get_proxy_for_country($country) {
        // Return country-specific proxy if available
        $country_proxies = $this->get_country_proxies($country);
        
        if (!empty($country_proxies)) {
            return $this->select_healthy_proxy($country_proxies);
        }
        
        return null; // No proxy needed
    }
    
    /**
     * Get proxies for specific country
     */
    private function get_country_proxies($country) {
        $country_map = array(
            'Ghana' => 'GH',
            'Kenya' => 'KE',
            'Nigeria' => 'NG',
            'South Africa' => 'ZA'
        );
        
        $country_code = $country_map[$country] ?? null;
        
        if (!$country_code) {
            return array();
        }
        
        return array_filter($this->proxy_list, function($proxy) use ($country_code) {
            return isset($proxy['country']) && $proxy['country'] === $country_code;
        });
    }
    
    /**
     * Select healthy proxy from list
     */
    private function select_healthy_proxy($proxies) {
        foreach ($proxies as $proxy) {
            if ($this->is_proxy_healthy($proxy)) {
                return $proxy;
            }
        }
        
        return null;
    }
    
    /**
     * Check proxy health
     */
    private function is_proxy_healthy($proxy) {
        $proxy_id = $proxy['host'] . ':' . $proxy['port'];
        
        // Check if we have recent health data
        if (isset($this->proxy_health[$proxy_id])) {
            $health_data = $this->proxy_health[$proxy_id];
            
            // Consider proxy healthy if last check was successful and recent
            if ($health_data['status'] === 'healthy' && 
                (time() - $health_data['last_check']) < 3600) { // 1 hour
                return true;
            }
        }
        
        // Perform health check
        return $this->perform_proxy_health_check($proxy);
    }
    
    /**
     * Perform proxy health check
     */
    private function perform_proxy_health_check($proxy) {
        // Simple health check - attempt to connect
        $proxy_id = $proxy['host'] . ':' . $proxy['port'];
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://httpbin.org/ip');
            curl_setopt($ch, CURLOPT_PROXY, $proxy['host'] . ':' . $proxy['port']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            
            if (isset($proxy['username']) && isset($proxy['password'])) {
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy['username'] . ':' . $proxy['password']);
            }
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $is_healthy = ($http_code === 200 && !empty($response));
            
            $this->proxy_health[$proxy_id] = array(
                'status' => $is_healthy ? 'healthy' : 'unhealthy',
                'last_check' => time(),
                'response_time' => curl_getinfo($ch, CURLINFO_TOTAL_TIME)
            );
            
            return $is_healthy;
            
        } catch (Exception $e) {
            $this->proxy_health[$proxy_id] = array(
                'status' => 'unhealthy',
                'last_check' => time(),
                'error' => $e->getMessage()
            );
            
            return false;
        }
    }
}

/**
 * Session Manager for Authenticated Content
 */
class ChatGABI_Session_Manager {
    
    private $sessions;
    private $session_storage;
    
    public function __construct() {
        $this->sessions = array();
        $this->session_storage = WP_CONTENT_DIR . '/chatgabi-sessions/';
        
        // Create session storage directory
        if (!file_exists($this->session_storage)) {
            wp_mkdir_p($this->session_storage);
        }
    }
    
    /**
     * Get session for specific source
     */
    public function get_session($source_id) {
        if (isset($this->sessions[$source_id])) {
            return $this->sessions[$source_id];
        }
        
        // Load session from storage
        $session_file = $this->session_storage . $source_id . '.json';
        
        if (file_exists($session_file)) {
            $session_data = json_decode(file_get_contents($session_file), true);
            
            if ($session_data && $this->is_session_valid($session_data)) {
                $this->sessions[$source_id] = $session_data;
                return $session_data;
            }
        }
        
        return null;
    }
    
    /**
     * Create new session for source
     */
    public function create_session($source_id, $login_data) {
        $session_data = array(
            'source_id' => $source_id,
            'cookies' => array(),
            'headers' => array(),
            'created_at' => time(),
            'last_used' => time(),
            'login_data' => $login_data
        );
        
        // Perform login and capture session data
        $login_result = $this->perform_login($login_data);
        
        if ($login_result) {
            $session_data['cookies'] = $login_result['cookies'];
            $session_data['headers'] = $login_result['headers'];
            
            // Store session
            $this->store_session($source_id, $session_data);
            $this->sessions[$source_id] = $session_data;
            
            return $session_data;
        }
        
        return null;
    }
    
    /**
     * Check if session is valid
     */
    private function is_session_valid($session_data) {
        // Check if session is not expired (24 hours)
        $max_age = 24 * 60 * 60; // 24 hours
        
        return (time() - $session_data['last_used']) < $max_age;
    }
    
    /**
     * Store session to file
     */
    private function store_session($source_id, $session_data) {
        $session_file = $this->session_storage . $source_id . '.json';
        file_put_contents($session_file, json_encode($session_data));
    }
}

/**
 * Intelligent Rate Limiter
 */
class ChatGABI_Rate_Limiter {
    
    private $rate_limits;
    private $request_history;
    
    public function __construct() {
        $this->rate_limits = array();
        $this->request_history = array();
        $this->load_rate_limit_config();
    }
    
    /**
     * Load rate limiting configuration
     */
    private function load_rate_limit_config() {
        $this->rate_limits = array(
            'default' => array(
                'requests_per_minute' => 10,
                'requests_per_hour' => 300,
                'burst_limit' => 5
            ),
            'government' => array(
                'requests_per_minute' => 5,
                'requests_per_hour' => 100,
                'burst_limit' => 2
            ),
            'financial' => array(
                'requests_per_minute' => 8,
                'requests_per_hour' => 200,
                'burst_limit' => 3
            ),
            'industry' => array(
                'requests_per_minute' => 12,
                'requests_per_hour' => 400,
                'burst_limit' => 6
            )
        );
    }
    
    /**
     * Apply intelligent delay based on source type and country
     */
    public function apply_intelligent_delay($country, $source_count) {
        $base_delay = 1; // 1 second base delay
        
        // Adjust delay based on source count
        $source_multiplier = min(2.0, $source_count / 10);
        
        // Country-specific adjustments
        $country_multipliers = array(
            'Ghana' => 1.0,
            'Kenya' => 1.2,
            'Nigeria' => 1.5,
            'South Africa' => 1.1
        );
        
        $country_multiplier = $country_multipliers[$country] ?? 1.0;
        
        $delay = $base_delay * $source_multiplier * $country_multiplier;
        
        // Add random jitter to avoid patterns
        $jitter = rand(0, 500) / 1000; // 0-0.5 seconds
        
        sleep($delay + $jitter);
    }
    
    /**
     * Worker-level delay
     */
    public function worker_delay($worker_id) {
        // Shorter delay for workers
        $delay = rand(500, 1500) / 1000; // 0.5-1.5 seconds
        usleep($delay * 1000000);
    }
    
    /**
     * Check if request is allowed
     */
    public function is_request_allowed($source_type, $source_id) {
        $limits = $this->rate_limits[$source_type] ?? $this->rate_limits['default'];
        
        $current_time = time();
        $minute_ago = $current_time - 60;
        $hour_ago = $current_time - 3600;
        
        // Initialize history if not exists
        if (!isset($this->request_history[$source_id])) {
            $this->request_history[$source_id] = array();
        }
        
        $history = $this->request_history[$source_id];
        
        // Count recent requests
        $requests_last_minute = count(array_filter($history, function($timestamp) use ($minute_ago) {
            return $timestamp > $minute_ago;
        }));
        
        $requests_last_hour = count(array_filter($history, function($timestamp) use ($hour_ago) {
            return $timestamp > $hour_ago;
        }));
        
        // Check limits
        if ($requests_last_minute >= $limits['requests_per_minute'] ||
            $requests_last_hour >= $limits['requests_per_hour']) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Record request
     */
    public function record_request($source_type, $source_id) {
        if (!isset($this->request_history[$source_id])) {
            $this->request_history[$source_id] = array();
        }
        
        $this->request_history[$source_id][] = time();
        
        // Clean old history (keep only last hour)
        $hour_ago = time() - 3600;
        $this->request_history[$source_id] = array_filter(
            $this->request_history[$source_id],
            function($timestamp) use ($hour_ago) {
                return $timestamp > $hour_ago;
            }
        );
    }
}

/**
 * Scraping Performance Monitor
 *
 * Note: Renamed from ChatGABI_Performance_Monitor to avoid conflict
 * with the main performance monitor class in performance-monitor.php
 */
class ChatGABI_Scraping_Performance_Monitor {
    
    private $metrics;
    private $cycle_start_time;
    
    public function __construct() {
        $this->metrics = array();
        $this->cycle_start_time = null;
    }
    
    /**
     * Start performance cycle
     */
    public function start_cycle($cycle_type) {
        $this->cycle_start_time = microtime(true);
        $this->metrics[$cycle_type] = array(
            'start_time' => $this->cycle_start_time,
            'data_points_processed' => 0,
            'sources_scraped' => 0,
            'errors_encountered' => 0,
            'success_rate' => 0
        );
    }
    
    /**
     * End performance cycle
     */
    public function end_cycle($cycle_type, $success) {
        if (!isset($this->metrics[$cycle_type])) {
            return;
        }
        
        $end_time = microtime(true);
        $this->metrics[$cycle_type]['end_time'] = $end_time;
        $this->metrics[$cycle_type]['duration'] = $end_time - $this->metrics[$cycle_type]['start_time'];
        $this->metrics[$cycle_type]['success'] = $success;
        
        // Calculate performance metrics
        $this->calculate_performance_metrics($cycle_type);
        
        // Store metrics in database
        $this->store_performance_metrics($cycle_type);
    }
    
    /**
     * Get current metrics
     */
    public function get_current_metrics() {
        return $this->metrics;
    }
    
    /**
     * Calculate performance metrics
     */
    private function calculate_performance_metrics($cycle_type) {
        $metrics = &$this->metrics[$cycle_type];
        
        if ($metrics['sources_scraped'] > 0) {
            $metrics['success_rate'] = (($metrics['sources_scraped'] - $metrics['errors_encountered']) / $metrics['sources_scraped']) * 100;
        }
        
        if ($metrics['duration'] > 0) {
            $metrics['data_points_per_hour'] = ($metrics['data_points_processed'] / $metrics['duration']) * 3600;
        }
    }
    
    /**
     * Store performance metrics
     */
    private function store_performance_metrics($cycle_type) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_performance_metrics';
        
        $wpdb->insert(
            $table_name,
            array(
                'cycle_type' => $cycle_type,
                'metrics_data' => json_encode($this->metrics[$cycle_type]),
                'timestamp' => current_time('mysql')
            ),
            array('%s', '%s', '%s')
        );
    }
}
