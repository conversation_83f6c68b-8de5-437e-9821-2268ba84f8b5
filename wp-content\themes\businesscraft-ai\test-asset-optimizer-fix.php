<?php
/**
 * Test Asset Optimizer Class Loading Fix
 * 
 * This script tests that the ChatGABI_Asset_Optimizer class and other
 * performance optimization classes are properly loaded and accessible.
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die("❌ Could not load WordPress\n");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI Asset Optimizer Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 5px; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>⚡ ChatGABI Asset Optimizer Class Loading Fix Test</h1>
    
    <div class="test-section">
        <h2>1. Required Classes Existence Check</h2>
        <?php
        $required_classes = [
            'ChatGABI_Asset_Optimizer',
            'ChatGABI_Database_Optimizer', 
            'ChatGABI_Performance_Monitor',
            'ChatGABI_Memory_Optimizer',
            'ChatGABI_Advanced_Cache_Manager',
            'ChatGABI_Advanced_Performance_Optimization'
        ];
        
        $classes_exist = [];
        foreach ($required_classes as $class) {
            $exists = class_exists($class);
            $classes_exist[$class] = $exists;
            ?>
            <div class="test-result <?php echo $exists ? 'pass' : 'fail'; ?>">
                <?php echo $exists ? '✅' : '❌'; ?> 
                <code><?php echo $class; ?></code> class 
                <?php echo $exists ? 'exists' : 'not found'; ?>
            </div>
            <?php
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>2. Advanced Performance Optimization Instantiation Test</h2>
        <?php
        $instantiation_success = false;
        $instantiation_error = '';
        
        $all_classes_exist = !in_array(false, $classes_exist);
        
        if ($all_classes_exist) {
            try {
                $performance_optimization = new ChatGABI_Advanced_Performance_Optimization();
                $instantiation_success = true;
            } catch (Exception $e) {
                $instantiation_error = $e->getMessage();
            } catch (Error $e) {
                $instantiation_error = $e->getMessage();
            }
        }
        ?>
        <div class="test-result <?php echo $instantiation_success ? 'pass' : 'fail'; ?>">
            <?php echo $instantiation_success ? '✅' : '❌'; ?> 
            Advanced Performance Optimization instantiation <?php echo $instantiation_success ? 'successful' : 'failed'; ?>
            <?php if ($instantiation_error): ?>
                <br><small>Error: <?php echo esc_html($instantiation_error); ?></small>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Individual Component Instantiation Tests</h2>
        <?php
        $components = [
            'ChatGABI_Asset_Optimizer' => 'Asset Optimizer',
            'ChatGABI_Database_Optimizer' => 'Database Optimizer',
            'ChatGABI_Performance_Monitor' => 'Performance Monitor',
            'ChatGABI_Memory_Optimizer' => 'Memory Optimizer',
            'ChatGABI_Advanced_Cache_Manager' => 'Advanced Cache Manager'
        ];
        
        $component_results = [];
        foreach ($components as $class => $name) {
            $success = false;
            $error = '';
            
            if (class_exists($class)) {
                try {
                    $instance = new $class();
                    $success = true;
                    $component_results[$class] = $instance;
                } catch (Exception $e) {
                    $error = $e->getMessage();
                } catch (Error $e) {
                    $error = $e->getMessage();
                }
            }
            ?>
            <div class="test-result <?php echo $success ? 'pass' : 'fail'; ?>">
                <?php echo $success ? '✅' : '❌'; ?> 
                <?php echo $name; ?> instantiation <?php echo $success ? 'successful' : 'failed'; ?>
                <?php if ($error): ?>
                    <br><small>Error: <?php echo esc_html($error); ?></small>
                <?php endif; ?>
            </div>
            <?php
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>4. Functionality Tests</h2>
        <?php
        $functionality_tests = [];
        
        // Test Asset Optimizer functionality
        if (isset($component_results['ChatGABI_Asset_Optimizer'])) {
            try {
                $asset_optimizer = $component_results['ChatGABI_Asset_Optimizer'];
                if (method_exists($asset_optimizer, 'get_loaded_assets')) {
                    $loaded_assets = $asset_optimizer->get_loaded_assets();
                    $functionality_tests['asset_optimizer'] = true;
                } else {
                    $functionality_tests['asset_optimizer'] = false;
                }
            } catch (Exception $e) {
                $functionality_tests['asset_optimizer'] = false;
            }
        }
        
        // Test Database Optimizer functionality
        if (isset($component_results['ChatGABI_Database_Optimizer'])) {
            try {
                $db_optimizer = $component_results['ChatGABI_Database_Optimizer'];
                if (method_exists($db_optimizer, 'get_performance_stats')) {
                    $db_stats = $db_optimizer->get_performance_stats();
                    $functionality_tests['database_optimizer'] = true;
                } else {
                    $functionality_tests['database_optimizer'] = false;
                }
            } catch (Exception $e) {
                $functionality_tests['database_optimizer'] = false;
            }
        }
        
        // Test Performance Monitor functionality
        if (isset($component_results['ChatGABI_Performance_Monitor'])) {
            try {
                $perf_monitor = $component_results['ChatGABI_Performance_Monitor'];
                if (method_exists($perf_monitor, 'get_performance_data')) {
                    $perf_data = $perf_monitor->get_performance_data();
                    $functionality_tests['performance_monitor'] = true;
                } else {
                    $functionality_tests['performance_monitor'] = false;
                }
            } catch (Exception $e) {
                $functionality_tests['performance_monitor'] = false;
            }
        }
        
        foreach ($functionality_tests as $component => $success) {
            ?>
            <div class="test-result <?php echo $success ? 'pass' : 'fail'; ?>">
                <?php echo $success ? '✅' : '❌'; ?> 
                <?php echo ucwords(str_replace('_', ' ', $component)); ?> functionality test 
                <?php echo $success ? 'passed' : 'failed'; ?>
            </div>
            <?php
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>5. File Loading Order Verification</h2>
        <?php
        $functions_file = get_template_directory() . '/functions.php';
        $functions_content = file_get_contents($functions_file);
        
        // Check loading order
        $asset_opt_pos = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/asset-optimization.php';");
        $db_opt_pos = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/database-optimization.php';");
        $perf_mon_pos = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/performance-monitor.php';");
        $advanced_perf_pos = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/advanced-performance-optimization.php';");
        
        $correct_order = ($asset_opt_pos !== false && $db_opt_pos !== false && $perf_mon_pos !== false && 
                         $advanced_perf_pos !== false && 
                         $asset_opt_pos < $advanced_perf_pos && 
                         $db_opt_pos < $advanced_perf_pos && 
                         $perf_mon_pos < $advanced_perf_pos);
        ?>
        <div class="test-result <?php echo $correct_order ? 'pass' : 'fail'; ?>">
            <?php echo $correct_order ? '✅' : '❌'; ?> 
            Correct file loading order (dependencies before advanced-performance-optimization.php)
        </div>
        
        <?php if ($correct_order): ?>
            <div class="info">
                <strong>Loading Order Verified:</strong>
                <ul>
                    <li>asset-optimization.php: Position <?php echo $asset_opt_pos; ?></li>
                    <li>database-optimization.php: Position <?php echo $db_opt_pos; ?></li>
                    <li>performance-monitor.php: Position <?php echo $perf_mon_pos; ?></li>
                    <li>advanced-performance-optimization.php: Position <?php echo $advanced_perf_pos; ?></li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>6. Class Redeclaration Check</h2>
        <?php
        $advanced_perf_file = get_template_directory() . '/inc/advanced-performance-optimization.php';
        $advanced_perf_content = file_get_contents($advanced_perf_file);
        
        $has_duplicate_db_class = strpos($advanced_perf_content, 'class ChatGABI_Database_Optimizer') !== false;
        $has_comment_instead = strpos($advanced_perf_content, 'ChatGABI_Database_Optimizer class is now defined in database-optimization.php') !== false;
        ?>
        <div class="test-result <?php echo !$has_duplicate_db_class ? 'pass' : 'fail'; ?>">
            <?php echo !$has_duplicate_db_class ? '✅' : '❌'; ?> 
            No duplicate ChatGABI_Database_Optimizer class in advanced-performance-optimization.php
        </div>
        
        <div class="test-result <?php echo $has_comment_instead ? 'pass' : 'fail'; ?>">
            <?php echo $has_comment_instead ? '✅' : '❌'; ?> 
            Explanatory comment added in place of duplicate class
        </div>
    </div>
    
    <div class="test-section">
        <h2>7. Overall Status</h2>
        <?php
        $all_tests_passed = $all_classes_exist && $instantiation_success && 
                           !empty($functionality_tests) && !in_array(false, $functionality_tests) && 
                           $correct_order && !$has_duplicate_db_class && $has_comment_instead;
        ?>
        
        <div class="test-result <?php echo $all_tests_passed ? 'pass' : 'fail'; ?>">
            <?php echo $all_tests_passed ? '✅' : '❌'; ?> 
            <strong><?php echo $all_tests_passed ? 'SUCCESS' : 'ISSUES DETECTED'; ?>:</strong> 
            Asset Optimizer dependency <?php echo $all_tests_passed ? 'resolved' : 'needs attention'; ?>
        </div>
        
        <?php if ($all_tests_passed): ?>
            <div class="info">
                <strong>Resolution Summary:</strong>
                <ul>
                    <li>All required performance optimization classes properly loaded</li>
                    <li>Advanced Performance Optimization can instantiate successfully</li>
                    <li>Correct file loading order established</li>
                    <li>Duplicate class definitions removed</li>
                    <li>All asset optimization features functional</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="info">
        <strong>Next Steps:</strong>
        <ul>
            <li>Test the ChatGABI website to ensure performance optimization works</li>
            <li>Verify that asset optimization features are functioning</li>
            <li>Check that database optimization is working properly</li>
            <li>Monitor error logs for any remaining dependency issues</li>
        </ul>
    </div>
</body>
</html>
