<?php
/**
 * Memory Usage Optimization for ChatGABI
 * 
 * Handles memory management, lazy loading, and resource optimization
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Memory Optimizer Class
 */
class ChatGABI_Memory_Optimizer {
    
    private static $instance = null;
    private $memory_stats = array();
    private $lazy_loaded_modules = array();
    private $memory_threshold = 128; // MB
    private $heavy_modules = array();
    private $object_cache = array();
    private $cache_limits = array();
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_memory_tracking();
        $this->set_memory_limits();
        $this->init_hooks();
        $this->identify_heavy_modules();
    }

    /**
     * Initialize memory optimization
     */
    public function init_memory_optimization() {
        // Set up memory monitoring
        $this->init_memory_tracking();

        // Configure memory limits
        $this->set_memory_limits();

        // Initialize lazy loading
        $this->init_lazy_loading();

        // Set up cleanup schedules
        $this->schedule_memory_cleanup();

        return true;
    }

    /**
     * Get efficiency score
     */
    public function get_efficiency_score() {
        $stats = $this->get_memory_stats();

        $score = 100;

        // Penalize high memory usage
        $memory_percentage = ($stats['current_usage'] / $stats['memory_threshold']) * 100;
        if ($memory_percentage > 80) {
            $score -= ($memory_percentage - 80) * 2;
        }

        // Bonus for memory savings
        if (isset($stats['memory_saved']) && $stats['memory_saved'] > 0) {
            $score += min(20, $stats['memory_saved'] / 1024 / 1024); // Bonus for MB saved
        }

        // Penalize if near memory limit
        if ($memory_percentage > 90) {
            $score -= 30; // Heavy penalty for critical memory usage
        }

        return max(0, min(100, round($score, 2)));
    }

    /**
     * Optimize memory
     */
    public function optimize_memory() {
        $results = array();

        // Run aggressive cleanup
        $this->aggressive_memory_cleanup();
        $results['aggressive_cleanup'] = true;

        // Clear object cache
        $this->object_cache = array();
        $results['object_cache_cleared'] = true;

        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            $collected = gc_collect_cycles();
            $results['garbage_collected'] = $collected;
        }

        // Update memory stats
        $results['memory_stats'] = $this->get_memory_stats();

        return $results;
    }

    /**
     * Cleanup memory
     */
    public function cleanup_memory() {
        // Run moderate cleanup
        $this->moderate_memory_cleanup();

        // Update memory statistics
        $this->memory_stats['cleanup_time'] = time();

        return true;
    }

    /**
     * Cleanup temporary data
     */
    public function cleanup_temporary_data() {
        // Clear temporary object cache
        $temp_keys = array_filter(array_keys($this->object_cache), function($key) {
            return strpos($key, 'temp_') === 0;
        });

        foreach ($temp_keys as $key) {
            unset($this->object_cache[$key]);
        }

        // Clear temporary transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_chatgabi_temp_%'");

        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        return true;
    }

    /**
     * Initialize lazy loading
     */
    private function init_lazy_loading() {
        // Set up lazy loading for heavy modules
        $this->lazy_loaded_modules = array(
            'chatgabi_analytics' => false,
            'chatgabi_advanced_features' => false,
            'chatgabi_export_tools' => false,
            'chatgabi_collaboration' => false
        );

        return true;
    }

    /**
     * Schedule memory cleanup
     */
    private function schedule_memory_cleanup() {
        // Schedule regular cleanup
        if (!wp_next_scheduled('chatgabi_memory_cleanup')) {
            wp_schedule_event(time(), 'hourly', 'chatgabi_memory_cleanup');
        }

        add_action('chatgabi_memory_cleanup', array($this, 'cleanup_memory'));

        return true;
    }

    /**
     * Initialize memory tracking
     */
    private function init_memory_tracking() {
        $this->memory_stats['start_time'] = microtime(true);
        $this->memory_stats['start_memory'] = memory_get_usage(true);
        $this->memory_stats['peak_memory'] = memory_get_peak_usage(true);
        $this->memory_stats['modules_loaded'] = array();
        $this->memory_stats['heavy_modules_deferred'] = array();
    }
    
    /**
     * Set memory limits and thresholds
     */
    private function set_memory_limits() {
        // Adjust based on server capacity
        $available_memory = $this->get_available_memory();
        
        if ($available_memory < 256) {
            $this->memory_threshold = 64; // Conservative for low-memory servers
        } elseif ($available_memory < 512) {
            $this->memory_threshold = 128;
        } else {
            $this->memory_threshold = 256;
        }
        
        // Set cache limits based on available memory
        $this->cache_limits = array(
            'object_cache_max_items' => min(1000, $available_memory * 2),
            'query_cache_max_size' => min(50, $available_memory / 10), // MB
            'template_cache_max_items' => min(500, $available_memory),
            'user_cache_max_items' => min(200, $available_memory / 2)
        );
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('init', array($this, 'check_memory_usage'), 1);
        add_action('wp_footer', array($this, 'memory_cleanup'), 999);
        add_action('admin_footer', array($this, 'memory_cleanup'), 999);
        add_action('wp_head', array($this, 'preload_critical_resources'), 1);
        
        // Memory monitoring hooks
        add_action('shutdown', array($this, 'log_memory_usage'));
        
        // Module loading optimization
        add_filter('chatgabi_should_load_module', array($this, 'should_load_module'), 10, 2);
        add_action('chatgabi_before_heavy_operation', array($this, 'prepare_for_heavy_operation'));
        add_action('chatgabi_after_heavy_operation', array($this, 'cleanup_after_heavy_operation'));
    }
    
    /**
     * Identify heavy modules that can be lazy-loaded
     */
    private function identify_heavy_modules() {
        $this->heavy_modules = array(
            // Analytics and reporting modules
            'advanced-analytics' => array(
                'file' => '/inc/advanced-analytics.php',
                'conditions' => array('is_page' => array('analytics', 'dashboard')),
                'memory_cost' => 15, // MB
                'defer_until' => 'user_interaction'
            ),
            
            // Export and PDF generation
            'pdf-export' => array(
                'file' => '/inc/pdf-export.php',
                'conditions' => array('action' => array('export', 'download')),
                'memory_cost' => 25,
                'defer_until' => 'export_request'
            ),
            
            // Advanced scraping system
            'web-scraper' => array(
                'file' => '/inc/advanced-web-scraper.php',
                'conditions' => array('is_admin' => true, 'action' => 'scrape'),
                'memory_cost' => 30,
                'defer_until' => 'admin_request'
            ),
            
            // AI agent network
            'ai-agents' => array(
                'file' => '/inc/ai-agent-network.php',
                'conditions' => array('wp_doing_cron' => true),
                'memory_cost' => 20,
                'defer_until' => 'cron_execution'
            ),
            
            // WhatsApp integration
            'whatsapp' => array(
                'file' => '/inc/whatsapp-integration.php',
                'conditions' => array('param' => 'whatsapp'),
                'memory_cost' => 12,
                'defer_until' => 'whatsapp_request'
            ),
            
            // Translation services
            'translation' => array(
                'file' => '/inc/translation-service.php',
                'conditions' => array('param' => 'translate'),
                'memory_cost' => 18,
                'defer_until' => 'translation_request'
            ),
            
            // Advanced personalization
            'personalization' => array(
                'file' => '/inc/context-personalization.php',
                'conditions' => array('user_logged_in' => true),
                'memory_cost' => 10,
                'defer_until' => 'user_activity'
            )
        );
    }
    
    /**
     * Check if a module should be loaded
     */
    public function should_load_module($should_load, $module_name) {
        // Always load critical modules
        $critical_modules = array(
            'secure-api-key-manager',
            'enhanced-input-validator',
            'unified-error-handler',
            'database-optimization'
        );
        
        if (in_array($module_name, $critical_modules)) {
            return true;
        }
        
        // Check memory usage before loading heavy modules
        if (isset($this->heavy_modules[$module_name])) {
            $current_memory = $this->get_current_memory_usage();
            $module_cost = $this->heavy_modules[$module_name]['memory_cost'];
            
            if ($current_memory + $module_cost > $this->memory_threshold) {
                $this->defer_module_loading($module_name);
                return false;
            }
        }
        
        return $should_load;
    }
    
    /**
     * Defer module loading until needed
     */
    private function defer_module_loading($module_name) {
        $this->memory_stats['heavy_modules_deferred'][] = $module_name;
        
        // Register lazy loader
        add_action('wp_ajax_chatgabi_load_module', array($this, 'ajax_load_deferred_module'));
        add_action('wp_ajax_nopriv_chatgabi_load_module', array($this, 'ajax_load_deferred_module'));
        
        // Add JavaScript to handle lazy loading
        add_action('wp_footer', function() use ($module_name) {
            $defer_condition = $this->heavy_modules[$module_name]['defer_until'];
            ?>
            <script>
            (function() {
                var moduleLoaded = false;
                var moduleName = '<?php echo esc_js($module_name); ?>';
                var condition = '<?php echo esc_js($defer_condition); ?>';
                
                function loadModule() {
                    if (moduleLoaded) return;
                    moduleLoaded = true;
                    
                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '<?php echo admin_url('admin-ajax.php'); ?>');
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.send('action=chatgabi_load_module&module=' + moduleName + '&nonce=<?php echo wp_create_nonce('chatgabi_load_module'); ?>');
                }
                
                // Load based on condition
                switch(condition) {
                    case 'user_interaction':
                        document.addEventListener('click', loadModule, {once: true});
                        document.addEventListener('scroll', loadModule, {once: true});
                        break;
                    case 'export_request':
                        document.addEventListener('click', function(e) {
                            if (e.target.matches('[data-action="export"], .export-btn, .download-btn')) {
                                loadModule();
                            }
                        });
                        break;
                    case 'whatsapp_request':
                        document.addEventListener('click', function(e) {
                            if (e.target.matches('[data-whatsapp], .whatsapp-btn')) {
                                loadModule();
                            }
                        });
                        break;
                    case 'translation_request':
                        document.addEventListener('change', function(e) {
                            if (e.target.matches('select[name="language"], .language-selector')) {
                                loadModule();
                            }
                        });
                        break;
                    case 'user_activity':
                        setTimeout(loadModule, 3000); // Load after 3 seconds of activity
                        break;
                }
            })();
            </script>
            <?php
        });
    }
    
    /**
     * AJAX handler for loading deferred modules
     */
    public function ajax_load_deferred_module() {
        check_ajax_referer('chatgabi_load_module', 'nonce');
        
        $module_name = sanitize_text_field($_POST['module'] ?? '');
        
        if (!isset($this->heavy_modules[$module_name])) {
            wp_send_json_error('Invalid module');
            return;
        }
        
        $module_file = CHATGABI_THEME_DIR . $this->heavy_modules[$module_name]['file'];
        
        if (file_exists($module_file)) {
            // Check memory before loading
            $current_memory = $this->get_current_memory_usage();
            $module_cost = $this->heavy_modules[$module_name]['memory_cost'];
            
            if ($current_memory + $module_cost > $this->memory_threshold) {
                // Force cleanup before loading
                $this->aggressive_memory_cleanup();
            }
            
            require_once $module_file;
            $this->memory_stats['modules_loaded'][] = $module_name;
            
            wp_send_json_success('Module loaded: ' . $module_name);
        } else {
            wp_send_json_error('Module file not found');
        }
    }
    
    /**
     * Check current memory usage
     */
    public function check_memory_usage() {
        $current_memory = $this->get_current_memory_usage();
        
        if ($current_memory > $this->memory_threshold * 0.8) {
            // Approaching memory limit - start cleanup
            $this->moderate_memory_cleanup();
        }
        
        if ($current_memory > $this->memory_threshold * 0.9) {
            // Critical memory usage - aggressive cleanup
            $this->aggressive_memory_cleanup();
        }
        
        // Log warning if memory usage is high
        if ($current_memory > $this->memory_threshold) {
            chatgabi_log_error(
                'high_memory_usage',
                sprintf('Memory usage exceeded threshold: %dMB (limit: %dMB)', $current_memory, $this->memory_threshold),
                ChatGABI_Unified_Error_Handler::CATEGORY_SYSTEM,
                ChatGABI_Unified_Error_Handler::SEVERITY_HIGH,
                array('memory_usage' => $current_memory, 'threshold' => $this->memory_threshold)
            );
        }
    }
    
    /**
     * Moderate memory cleanup
     */
    private function moderate_memory_cleanup() {
        // Clear expired transients
        $this->clear_expired_transients();
        
        // Limit object cache size
        $this->limit_object_cache();
        
        // Clear old query results
        $this->clear_old_query_cache();
        
        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }
    
    /**
     * Aggressive memory cleanup
     */
    private function aggressive_memory_cleanup() {
        // All moderate cleanup actions
        $this->moderate_memory_cleanup();
        
        // Clear all object caches
        $this->object_cache = array();
        wp_cache_flush();
        
        // Clear all transients
        $this->clear_all_chatgabi_transients();
        
        // Unload non-essential modules
        $this->unload_non_essential_modules();
        
        // Force multiple garbage collections
        if (function_exists('gc_collect_cycles')) {
            for ($i = 0; $i < 3; $i++) {
                gc_collect_cycles();
            }
        }
    }
    
    /**
     * Clear expired transients
     */
    private function clear_expired_transients() {
        global $wpdb;
        
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%' AND option_value < UNIX_TIMESTAMP()");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' AND option_name NOT IN (SELECT CONCAT('_transient_', SUBSTRING(option_name, 19)) FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%')");
    }
    
    /**
     * Limit object cache size
     */
    private function limit_object_cache() {
        if (count($this->object_cache) > $this->cache_limits['object_cache_max_items']) {
            // Remove oldest entries
            $remove_count = count($this->object_cache) - $this->cache_limits['object_cache_max_items'];
            $this->object_cache = array_slice($this->object_cache, $remove_count, null, true);
        }
    }
    
    /**
     * Clear old query cache
     */
    private function clear_old_query_cache() {
        global $wpdb;
        
        // Clear ChatGABI query cache older than 1 hour
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s AND option_value < %d",
            '_transient_timeout_chatgabi_query_%',
            time() - 3600
        ));
    }
    
    /**
     * Clear all ChatGABI transients
     */
    private function clear_all_chatgabi_transients() {
        global $wpdb;
        
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_chatgabi_%' OR option_name LIKE '_transient_timeout_chatgabi_%'");
    }
    
    /**
     * Unload non-essential modules
     */
    private function unload_non_essential_modules() {
        // This is a placeholder - in practice, we'd track loaded modules
        // and selectively unload those that aren't currently needed
        
        // Clear heavy global variables
        global $chatgabi_heavy_data;
        if (isset($chatgabi_heavy_data)) {
            unset($chatgabi_heavy_data);
        }
        
        // Clear template caches
        if (function_exists('chatgabi_clear_template_cache')) {
            chatgabi_clear_template_cache();
        }
        
        // Clear query caches
        if (function_exists('chatgabi_clear_query_cache')) {
            chatgabi_clear_query_cache();
        }
    }
    
    /**
     * Preload critical resources
     */
    public function preload_critical_resources() {
        if (!is_admin()) {
            // Preload critical CSS and JS
            echo '<link rel="preload" href="' . CHATGABI_THEME_URL . '/style.css" as="style">';
            echo '<link rel="preload" href="' . includes_url('js/jquery/jquery.min.js') . '" as="script">';
            
            // DNS prefetch for external resources
            echo '<link rel="dns-prefetch" href="//cdn.jsdelivr.net">';
            echo '<link rel="dns-prefetch" href="//js.paystack.co">';
        }
    }
    
    /**
     * Prepare for heavy operation
     */
    public function prepare_for_heavy_operation() {
        // Clear caches before heavy operations
        $this->moderate_memory_cleanup();
        
        // Increase time limit for heavy operations
        if (!ini_get('safe_mode')) {
            set_time_limit(300); // 5 minutes
        }
    }
    
    /**
     * Cleanup after heavy operation
     */
    public function cleanup_after_heavy_operation() {
        // Force cleanup after heavy operations
        $this->aggressive_memory_cleanup();
        
        // Reset time limit
        if (!ini_get('safe_mode')) {
            set_time_limit(30);
        }
    }
    
    /**
     * Final memory cleanup
     */
    public function memory_cleanup() {
        // Light cleanup on page end
        $this->moderate_memory_cleanup();
        
        // Update memory stats
        $this->memory_stats['end_memory'] = memory_get_usage(true);
        $this->memory_stats['peak_memory'] = memory_get_peak_usage(true);
        $this->memory_stats['memory_saved'] = max(0, $this->memory_stats['start_memory'] - $this->memory_stats['end_memory']);
    }
    
    /**
     * Log memory usage statistics
     */
    public function log_memory_usage() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->memory_stats['execution_time'] = microtime(true) - $this->memory_stats['start_time'];
            
            $stats = array(
                'start_memory' => $this->format_bytes($this->memory_stats['start_memory']),
                'end_memory' => $this->format_bytes($this->memory_stats['end_memory']),
                'peak_memory' => $this->format_bytes($this->memory_stats['peak_memory']),
                'memory_saved' => $this->format_bytes($this->memory_stats['memory_saved']),
                'execution_time' => round($this->memory_stats['execution_time'], 3) . 's',
                'modules_loaded' => count($this->memory_stats['modules_loaded']),
                'modules_deferred' => count($this->memory_stats['heavy_modules_deferred'])
            );
            
            error_log('ChatGABI Memory Stats: ' . json_encode($stats));
            
            // Store stats for monitoring
            set_transient('chatgabi_memory_stats', $stats, 3600);
        }
    }
    
    /**
     * Get current memory usage in MB
     */
    private function get_current_memory_usage() {
        return round(memory_get_usage(true) / 1024 / 1024, 2);
    }
    
    /**
     * Get available memory from server
     */
    private function get_available_memory() {
        $memory_limit = ini_get('memory_limit');
        
        if ($memory_limit === '-1') {
            return 512; // Assume reasonable default for unlimited
        }
        
        // Convert to MB
        if (strpos($memory_limit, 'G') !== false) {
            return intval($memory_limit) * 1024;
        } elseif (strpos($memory_limit, 'M') !== false) {
            return intval($memory_limit);
        } elseif (strpos($memory_limit, 'K') !== false) {
            return intval($memory_limit) / 1024;
        }
        
        return intval($memory_limit) / 1024 / 1024; // Assume bytes
    }
    
    /**
     * Format bytes to human-readable string
     */
    private function format_bytes($size) {
        if ($size >= 1073741824) {
            return round($size / 1073741824, 2) . ' GB';
        } elseif ($size >= 1048576) {
            return round($size / 1048576, 2) . ' MB';
        } elseif ($size >= 1024) {
            return round($size / 1024, 2) . ' KB';
        }
        return $size . ' bytes';
    }
    
    /**
     * Get memory statistics
     */
    public function get_memory_stats() {
        return array_merge($this->memory_stats, array(
            'current_usage' => $this->get_current_memory_usage(),
            'available_memory' => $this->get_available_memory(),
            'memory_threshold' => $this->memory_threshold,
            'cache_limits' => $this->cache_limits,
            'heavy_modules' => array_keys($this->heavy_modules),
            'object_cache_size' => count($this->object_cache)
        ));
    }
    
    /**
     * Force memory optimization
     */
    public function force_memory_optimization() {
        $this->aggressive_memory_cleanup();
        return $this->get_memory_stats();
    }
}

/**
 * Global helper functions
 */

/**
 * Get memory optimizer instance
 */
function chatgabi_get_memory_optimizer() {
    return ChatGABI_Memory_Optimizer::get_instance();
}

/**
 * Get current memory usage
 */
function chatgabi_get_memory_usage() {
    $optimizer = ChatGABI_Memory_Optimizer::get_instance();
    return $optimizer->get_memory_stats();
}

/**
 * Force memory cleanup
 */
function chatgabi_force_memory_cleanup() {
    $optimizer = ChatGABI_Memory_Optimizer::get_instance();
    return $optimizer->force_memory_optimization();
}

/**
 * Check if module should be lazy-loaded
 */
function chatgabi_should_defer_module($module_name) {
    $optimizer = ChatGABI_Memory_Optimizer::get_instance();
    return !apply_filters('chatgabi_should_load_module', true, $module_name);
}

// Initialize memory optimizer
add_action('init', function() {
    ChatGABI_Memory_Optimizer::get_instance();
}, 1);
