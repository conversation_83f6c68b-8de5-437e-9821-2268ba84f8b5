<?php
/**
 * Verify ChatGABI Performance Optimization Fix
 * 
 * This script verifies that the singleton instantiation fix has been properly applied
 */

echo "🔧 ChatGABI Performance Optimization Fix Verification\n";
echo str_repeat("=", 60) . "\n\n";

// Check if the file exists
$file_path = 'wp-content/themes/businesscraft-ai/inc/advanced-performance-optimization.php';

if (!file_exists($file_path)) {
    echo "❌ ERROR: File not found: {$file_path}\n";
    exit(1);
}

echo "✅ File found: {$file_path}\n\n";

// Read the file content
$content = file_get_contents($file_path);

// Define the patterns we're looking for
$problematic_patterns = [
    'new ChatGABI_Database_Optimizer()',
    'new ChatGABI_Asset_Optimizer()',
    'new ChatGABI_Performance_Monitor()',
    'new ChatGABI_Memory_Optimizer()'
];

$correct_patterns = [
    'ChatGABI_Database_Optimizer::get_instance()',
    'ChatGABI_Asset_Optimizer::get_instance()',
    'ChatGABI_Performance_Monitor::get_instance()',
    'ChatGABI_Memory_Optimizer::get_instance()'
];

echo "1. Checking for problematic code (should NOT be found):\n";
$problems_found = 0;
foreach ($problematic_patterns as $pattern) {
    if (strpos($content, $pattern) !== false) {
        echo "   ❌ PROBLEM: Found '{$pattern}'\n";
        $problems_found++;
    } else {
        echo "   ✅ Good: '{$pattern}' not found\n";
    }
}

echo "\n2. Checking for correct singleton usage (should be found):\n";
$correct_found = 0;
foreach ($correct_patterns as $pattern) {
    if (strpos($content, $pattern) !== false) {
        echo "   ✅ Found: '{$pattern}'\n";
        $correct_found++;
    } else {
        echo "   ❌ Missing: '{$pattern}'\n";
    }
}

echo "\n3. Extracting constructor method:\n";
$constructor_start = strpos($content, 'public function __construct() {');
if ($constructor_start !== false) {
    $constructor_end = strpos($content, '}', $constructor_start);
    $constructor_code = substr($content, $constructor_start, $constructor_end - $constructor_start + 1);
    
    echo "   Constructor found:\n";
    $lines = explode("\n", $constructor_code);
    foreach ($lines as $i => $line) {
        echo "   " . ($i + 22) . ": " . trim($line) . "\n";
    }
} else {
    echo "   ❌ Constructor not found!\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "SUMMARY:\n";

if ($problems_found === 0 && $correct_found === count($correct_patterns)) {
    echo "🎉 SUCCESS! All fixes have been properly applied.\n";
    echo "✅ No problematic 'new' instantiations found\n";
    echo "✅ All singleton get_instance() calls are present\n";
    echo "✅ The fatal error should be resolved\n";
    echo "\nThe ChatGABI Advanced Performance Optimization system should now work correctly!\n";
} else {
    echo "❌ ISSUES DETECTED:\n";
    if ($problems_found > 0) {
        echo "   • {$problems_found} problematic instantiation(s) still exist\n";
    }
    if ($correct_found !== count($correct_patterns)) {
        $missing = count($correct_patterns) - $correct_found;
        echo "   • {$missing} correct singleton pattern(s) missing\n";
    }
    echo "\nPlease review the code and ensure all singleton classes use get_instance() method.\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
?>
