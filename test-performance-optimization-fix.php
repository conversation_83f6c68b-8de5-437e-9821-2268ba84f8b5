<?php
/**
 * Test ChatGABI Advanced Performance Optimization Fix
 * 
 * This test verifies that the singleton instantiation fix resolves the fatal error
 */

// WordPress environment setup
define('WP_USE_THEMES', false);
require_once('wp-config.php');
require_once('wp-load.php');

echo "🔧 ChatGABI Advanced Performance Optimization Fix Test\n";
echo "=" . str_repeat("=", 60) . "\n\n";

$tests_passed = 0;
$total_tests = 0;
$issues_found = array();

// Test 1: Check if required classes exist
echo "1. Testing Required Classes Existence:\n";
$required_classes = array(
    'ChatGABI_Database_Optimizer',
    'ChatGABI_Asset_Optimizer', 
    'ChatGABI_Performance_Monitor',
    'ChatGABI_Memory_Optimizer',
    'ChatGABI_Advanced_Cache_Manager'
);

foreach ($required_classes as $class) {
    $total_tests++;
    if (class_exists($class)) {
        echo "   ✅ {$class} class exists\n";
        $tests_passed++;
    } else {
        echo "   ❌ {$class} class missing\n";
        $issues_found[] = "{$class} class not found";
    }
}

echo "\n";

// Test 2: Check singleton pattern implementation
echo "2. Testing Singleton Pattern Implementation:\n";
$singleton_classes = array(
    'ChatGABI_Database_Optimizer',
    'ChatGABI_Asset_Optimizer',
    'ChatGABI_Performance_Monitor', 
    'ChatGABI_Memory_Optimizer'
);

foreach ($singleton_classes as $class) {
    $total_tests++;
    if (class_exists($class) && method_exists($class, 'get_instance')) {
        echo "   ✅ {$class} has get_instance() method\n";
        $tests_passed++;
    } else {
        echo "   ❌ {$class} missing get_instance() method\n";
        $issues_found[] = "{$class} singleton pattern not implemented";
    }
}

echo "\n";

// Test 3: Test singleton instantiation
echo "3. Testing Singleton Instantiation:\n";
foreach ($singleton_classes as $class) {
    $total_tests++;
    if (class_exists($class) && method_exists($class, 'get_instance')) {
        try {
            $instance = call_user_func(array($class, 'get_instance'));
            if (is_object($instance)) {
                echo "   ✅ {$class}::get_instance() works correctly\n";
                $tests_passed++;
            } else {
                echo "   ❌ {$class}::get_instance() returned invalid object\n";
                $issues_found[] = "{$class} get_instance() failed";
            }
        } catch (Exception $e) {
            echo "   ❌ {$class}::get_instance() threw exception: " . $e->getMessage() . "\n";
            $issues_found[] = "{$class} get_instance() exception: " . $e->getMessage();
        }
    } else {
        echo "   ⏭️  {$class} skipped (class or method missing)\n";
    }
}

echo "\n";

// Test 4: Test ChatGABI_Advanced_Performance_Optimization instantiation
echo "4. Testing Advanced Performance Optimization Instantiation:\n";
$total_tests++;

if (class_exists('ChatGABI_Advanced_Performance_Optimization')) {
    try {
        $performance_optimization = new ChatGABI_Advanced_Performance_Optimization();
        if (is_object($performance_optimization)) {
            echo "   ✅ ChatGABI_Advanced_Performance_Optimization instantiated successfully\n";
            $tests_passed++;
            
            // Test if the singleton instances are properly assigned
            $reflection = new ReflectionClass($performance_optimization);
            $properties = array(
                'database_optimizer' => 'ChatGABI_Database_Optimizer',
                'asset_optimizer' => 'ChatGABI_Asset_Optimizer',
                'performance_monitor' => 'ChatGABI_Performance_Monitor',
                'memory_optimizer' => 'ChatGABI_Memory_Optimizer'
            );
            
            foreach ($properties as $property => $expected_class) {
                $total_tests++;
                $prop = $reflection->getProperty($property);
                $prop->setAccessible(true);
                $value = $prop->getValue($performance_optimization);
                
                if (is_object($value) && get_class($value) === $expected_class) {
                    echo "   ✅ {$property} properly assigned as {$expected_class} instance\n";
                    $tests_passed++;
                } else {
                    echo "   ❌ {$property} not properly assigned\n";
                    $issues_found[] = "{$property} not properly assigned";
                }
            }
        } else {
            echo "   ❌ ChatGABI_Advanced_Performance_Optimization instantiation failed\n";
            $issues_found[] = "Advanced Performance Optimization instantiation failed";
        }
    } catch (Exception $e) {
        echo "   ❌ ChatGABI_Advanced_Performance_Optimization threw exception: " . $e->getMessage() . "\n";
        $issues_found[] = "Advanced Performance Optimization exception: " . $e->getMessage();
    } catch (Error $e) {
        echo "   ❌ ChatGABI_Advanced_Performance_Optimization threw fatal error: " . $e->getMessage() . "\n";
        $issues_found[] = "Advanced Performance Optimization fatal error: " . $e->getMessage();
    }
} else {
    echo "   ❌ ChatGABI_Advanced_Performance_Optimization class not found\n";
    $issues_found[] = "ChatGABI_Advanced_Performance_Optimization class missing";
}

echo "\n";

// Test 5: Test global function
echo "5. Testing Global Helper Function:\n";
$total_tests++;

if (function_exists('chatgabi_get_advanced_performance_optimization')) {
    try {
        $global_instance = chatgabi_get_advanced_performance_optimization();
        if (is_object($global_instance) && get_class($global_instance) === 'ChatGABI_Advanced_Performance_Optimization') {
            echo "   ✅ chatgabi_get_advanced_performance_optimization() works correctly\n";
            $tests_passed++;
        } else {
            echo "   ❌ chatgabi_get_advanced_performance_optimization() returned invalid object\n";
            $issues_found[] = "Global function returned invalid object";
        }
    } catch (Exception $e) {
        echo "   ❌ chatgabi_get_advanced_performance_optimization() threw exception: " . $e->getMessage() . "\n";
        $issues_found[] = "Global function exception: " . $e->getMessage();
    }
} else {
    echo "   ❌ chatgabi_get_advanced_performance_optimization() function not found\n";
    $issues_found[] = "Global helper function missing";
}

echo "\n";

// Summary
echo "=" . str_repeat("=", 60) . "\n";
echo "TEST SUMMARY:\n";
echo "Tests Passed: {$tests_passed}/{$total_tests}\n";
echo "Success Rate: " . round(($tests_passed / $total_tests) * 100, 1) . "%\n";

if (empty($issues_found)) {
    echo "\n🎉 ALL TESTS PASSED! The singleton instantiation fix is working correctly.\n";
    echo "✅ Fatal error should be resolved.\n";
} else {
    echo "\n❌ Issues Found:\n";
    foreach ($issues_found as $issue) {
        echo "   • {$issue}\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";
?>
