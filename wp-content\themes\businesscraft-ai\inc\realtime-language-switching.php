<?php
/**
 * Real-Time Language Switching System for ChatGABI
 * 
 * This file provides advanced real-time language switching with AI translation,
 * performance optimization, and seamless user experience.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class ChatGABI_Realtime_Language_Switching {
    
    private $ai_translation;
    private $performance_cache;
    private $switch_analytics;
    private $preload_manager;
    
    public function __construct() {
        $this->ai_translation = chatgabi_get_ai_translation();
        $this->performance_cache = new ChatGABI_Language_Cache();
        $this->switch_analytics = new ChatGABI_Language_Switch_Analytics();
        $this->preload_manager = new ChatGABI_Language_Preload_Manager();
        
        $this->init_hooks();
        $this->init_performance_optimizations();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // AJAX handlers for real-time switching
        add_action('wp_ajax_chatgabi_realtime_switch_language', array($this, 'ajax_realtime_switch_language'));
        add_action('wp_ajax_nopriv_chatgabi_realtime_switch_language', array($this, 'ajax_realtime_switch_language'));
        add_action('wp_ajax_chatgabi_preload_language_content', array($this, 'ajax_preload_language_content'));
        add_action('wp_ajax_chatgabi_get_switch_analytics', array($this, 'ajax_get_switch_analytics'));
        
        // Performance hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_realtime_assets'));
        add_action('wp_head', array($this, 'add_preload_hints'));
        
        // Language switching hooks
        add_action('chatgabi_language_switched', array($this, 'handle_language_switch'), 10, 3);
        add_filter('chatgabi_language_switch_data', array($this, 'enhance_switch_data'), 10, 3);
        
        // Background processing
        add_action('chatgabi_preload_languages', array($this, 'background_preload_languages'));
    }
    
    /**
     * Initialize performance optimizations
     */
    private function init_performance_optimizations() {
        // Enable output buffering for faster switching
        add_action('init', array($this, 'start_output_buffering'));

        // Preload critical language data
        add_action('wp_loaded', array($this, 'preload_critical_languages'));

        // Optimize database queries
        add_action('pre_get_posts', array($this, 'optimize_language_queries'));
    }

    /**
     * Optimize language-related database queries
     */
    public function optimize_language_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Add language-specific optimizations
            if (is_page() || is_single()) {
                // Optimize meta queries for language-specific content
                $meta_query = $query->get('meta_query');
                if (!$meta_query) {
                    $meta_query = array();
                }

                // Add language filter if needed
                $current_language = $this->get_current_language();
                if ($current_language && $current_language !== 'en') {
                    $meta_query[] = array(
                        'relation' => 'OR',
                        array(
                            'key' => 'chatgabi_language',
                            'value' => $current_language,
                            'compare' => '='
                        ),
                        array(
                            'key' => 'chatgabi_language',
                            'compare' => 'NOT EXISTS'
                        )
                    );
                    $query->set('meta_query', $meta_query);
                }
            }
        }
        return $query;
    }

    /**
     * Get current language
     */
    private function get_current_language() {
        if (is_user_logged_in()) {
            return get_user_meta(get_current_user_id(), 'chatgabi_preferred_language', true) ?: 'en';
        }
        return isset($_COOKIE['chatgabi_language']) ? sanitize_text_field($_COOKIE['chatgabi_language']) : 'en';
    }
    
    /**
     * Real-time language switching with AI translation
     */
    public function realtime_switch_language($target_language, $context = array()) {
        $start_time = microtime(true);
        
        // Get current language and user context
        $current_language = chatgabi_get_user_preferred_language();
        $user_id = get_current_user_id();
        $country_code = chatgabi_get_user_country();
        
        // Check if switch is needed
        if ($current_language === $target_language) {
            return array(
                'success' => true,
                'message' => __('Language already active', 'chatgabi'),
                'processing_time' => (microtime(true) - $start_time) * 1000
            );
        }
        
        // Get cached language data first
        $cached_data = $this->performance_cache->get_language_data($target_language, $country_code);
        
        if ($cached_data) {
            $switch_result = $this->apply_cached_language_switch($cached_data, $target_language, $context);
        } else {
            $switch_result = $this->perform_full_language_switch($target_language, $context);
        }
        
        // Update user preference
        if ($switch_result['success']) {
            $this->update_user_language_preference($user_id, $target_language);
            
            // Log analytics
            $this->switch_analytics->log_language_switch(array(
                'user_id' => $user_id,
                'from_language' => $current_language,
                'to_language' => $target_language,
                'processing_time' => (microtime(true) - $start_time) * 1000,
                'cache_hit' => !empty($cached_data),
                'context' => $context
            ));
            
            // Trigger language switch event
            do_action('chatgabi_language_switched', $user_id, $current_language, $target_language);
        }
        
        return $switch_result;
    }
    
    /**
     * Apply cached language switch
     */
    private function apply_cached_language_switch($cached_data, $target_language, $context) {
        $start_time = microtime(true);
        
        try {
            // Apply cached translations
            $ui_strings = $cached_data['ui_strings'];
            $template_content = $cached_data['template_content'];
            $cultural_context = $cached_data['cultural_context'];
            
            // Update session/user meta
            $this->update_language_session($target_language);
            
            return array(
                'success' => true,
                'language' => $target_language,
                'ui_strings' => $ui_strings,
                'template_content' => $template_content,
                'cultural_context' => $cultural_context,
                'currency_data' => $cached_data['currency_data'],
                'processing_time' => (microtime(true) - $start_time) * 1000,
                'cache_hit' => true,
                'message' => __('Language switched successfully (cached)', 'chatgabi')
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'processing_time' => (microtime(true) - $start_time) * 1000
            );
        }
    }
    
    /**
     * Perform full language switch with AI translation
     */
    private function perform_full_language_switch($target_language, $context) {
        $start_time = microtime(true);
        
        try {
            $country_code = chatgabi_get_user_country();
            
            // Get UI strings with AI translation
            $ui_strings = $this->get_translated_ui_strings($target_language, $country_code);
            
            // Get template content
            $template_content = $this->get_translated_template_content($target_language, $country_code, $context);
            
            // Get cultural context
            $cultural_context = $this->get_cultural_context($target_language, $country_code);
            
            // Get currency data
            $currency_data = chatgabi_get_js_currency_data()[$country_code] ?? array();
            
            // Cache the results for future use
            $cache_data = array(
                'ui_strings' => $ui_strings,
                'template_content' => $template_content,
                'cultural_context' => $cultural_context,
                'currency_data' => $currency_data,
                'generated_at' => time()
            );
            
            $this->performance_cache->set_language_data($target_language, $country_code, $cache_data);
            
            // Update session/user meta
            $this->update_language_session($target_language);
            
            return array(
                'success' => true,
                'language' => $target_language,
                'ui_strings' => $ui_strings,
                'template_content' => $template_content,
                'cultural_context' => $cultural_context,
                'currency_data' => $currency_data,
                'processing_time' => (microtime(true) - $start_time) * 1000,
                'cache_hit' => false,
                'message' => __('Language switched successfully', 'chatgabi')
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'processing_time' => (microtime(true) - $start_time) * 1000
            );
        }
    }
    
    /**
     * Get translated UI strings
     */
    private function get_translated_ui_strings($target_language, $country_code) {
        // Get base UI strings
        $base_strings = chatgabi_get_language_strings($target_language);
        
        // Enhance with AI translation for missing strings
        $enhanced_strings = array();
        
        foreach ($base_strings as $key => $value) {
            if (empty($value) || $value === $key) {
                // Use AI translation for missing strings
                $translation_result = $this->ai_translation->translate_content(
                    $key, 
                    'en', 
                    $target_language, 
                    array(
                        'country_code' => $country_code,
                        'business_context' => 'ui_strings'
                    )
                );
                
                $enhanced_strings[$key] = is_wp_error($translation_result) ? 
                    $value : 
                    $translation_result['translated_text'];
            } else {
                $enhanced_strings[$key] = $value;
            }
        }
        
        return $enhanced_strings;
    }
    
    /**
     * Get translated template content
     */
    private function get_translated_template_content($target_language, $country_code, $context) {
        $template_content = array();
        
        // Get active templates for user
        $active_templates = $this->get_user_active_templates();
        
        foreach ($active_templates as $template) {
            $translation_result = $this->ai_translation->translate_content(
                $template['content'], 
                'en', 
                $target_language, 
                array(
                    'country_code' => $country_code,
                    'business_context' => 'template',
                    'cultural_context' => true,
                    'business_terminology' => true
                )
            );
            
            $template_content[$template['id']] = is_wp_error($translation_result) ? 
                $template['content'] : 
                $translation_result['translated_text'];
        }
        
        return $template_content;
    }
    
    /**
     * Get cultural context for language
     */
    private function get_cultural_context($target_language, $country_code) {
        $cultural_enhancement = chatgabi_get_african_cultural_enhancement();
        return $cultural_enhancement->get_cultural_context($country_code, 'language_switching');
    }
    
    /**
     * Update user language preference
     */
    private function update_user_language_preference($user_id, $language) {
        if ($user_id) {
            update_user_meta($user_id, 'chatgabi_preferred_language', $language);
        } else {
            // Store in session for non-logged-in users
            if (!session_id()) {
                session_start();
            }
            $_SESSION['chatgabi_language'] = $language;
        }
    }
    
    /**
     * Update language session data
     */
    private function update_language_session($language) {
        // Update WordPress locale
        switch_to_locale(chatgabi_get_supported_languages()[$language]['locale'] ?? 'en_US');
        
        // Update session data
        if (!session_id()) {
            session_start();
        }
        $_SESSION['chatgabi_current_language'] = $language;
        $_SESSION['chatgabi_language_switched_at'] = time();
    }
    
    /**
     * Get user's active templates
     */
    private function get_user_active_templates() {
        global $wpdb;
        
        $user_id = get_current_user_id();
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        
        // Get recently used templates
        $templates = $wpdb->get_results($wpdb->prepare("
            SELECT id, title, prompt_content as content
            FROM {$templates_table}
            WHERE status = 'active'
            AND (is_public = 1 OR created_by = %d)
            ORDER BY usage_count DESC
            LIMIT 10
        ", $user_id), ARRAY_A);
        
        return $templates ?: array();
    }
    
    /**
     * AJAX handler for real-time language switching
     */
    public function ajax_realtime_switch_language() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_language_switch_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }
        
        $target_language = sanitize_text_field($_POST['language']);
        $context = array(
            'source' => sanitize_text_field($_POST['source'] ?? 'manual'),
            'page_type' => sanitize_text_field($_POST['page_type'] ?? 'general'),
            'user_agent' => sanitize_text_field($_SERVER['HTTP_USER_AGENT'] ?? '')
        );
        
        $switch_result = $this->realtime_switch_language($target_language, $context);
        
        if ($switch_result['success']) {
            wp_send_json_success($switch_result);
        } else {
            wp_send_json_error($switch_result);
        }
    }
    
    /**
     * AJAX handler for preloading language content
     */
    public function ajax_preload_language_content() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_preload_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }
        
        $languages = json_decode(stripslashes($_POST['languages']), true);
        $country_code = sanitize_text_field($_POST['country_code'] ?? 'GH');
        
        $preload_results = array();
        
        foreach ($languages as $language) {
            $preload_results[$language] = $this->preload_manager->preload_language_data($language, $country_code);
        }
        
        wp_send_json_success($preload_results);
    }
    
    /**
     * Enqueue real-time assets
     */
    public function enqueue_realtime_assets() {
        // Enqueue real-time language switching JavaScript
        wp_enqueue_script(
            'chatgabi-realtime-language-switching',
            get_template_directory_uri() . '/assets/js/realtime-language-switching.js',
            array('jquery', 'wp-i18n'),
            CHATGABI_VERSION,
            true
        );
        
        // Localize script with real-time data
        wp_localize_script('chatgabi-realtime-language-switching', 'chatgabiRealtimeLanguage', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'switchNonce' => wp_create_nonce('chatgabi_language_switch_nonce'),
            'preloadNonce' => wp_create_nonce('chatgabi_preload_nonce'),
            'currentLanguage' => chatgabi_get_user_preferred_language(),
            'currentCountry' => chatgabi_get_user_country(),
            'supportedLanguages' => chatgabi_get_supported_languages(),
            'performanceMode' => get_option('chatgabi_performance_mode', 'balanced'),
            'preloadEnabled' => get_option('chatgabi_preload_enabled', true),
            'strings' => array(
                'switching_language' => __('Switching language...', 'chatgabi'),
                'language_switched' => __('Language switched successfully', 'chatgabi'),
                'switch_failed' => __('Failed to switch language', 'chatgabi'),
                'preloading' => __('Preloading language data...', 'chatgabi'),
                'preload_complete' => __('Language data preloaded', 'chatgabi')
            )
        ));
        
        // Set script translations
        wp_set_script_translations('chatgabi-realtime-language-switching', 'chatgabi', get_template_directory() . '/languages');
    }
    
    /**
     * Add preload hints for performance
     */
    public function add_preload_hints() {
        $user_country = chatgabi_get_user_country();
        $current_language = chatgabi_get_user_preferred_language();
        
        // Preload likely next languages based on country
        $likely_languages = $this->get_likely_languages($user_country, $current_language);
        
        foreach ($likely_languages as $language) {
            echo '<link rel="prefetch" href="' . admin_url('admin-ajax.php') . '?action=chatgabi_preload_language_content&language=' . $language . '">' . "\n";
        }
    }
    
    /**
     * Get likely languages for preloading
     */
    private function get_likely_languages($country_code, $current_language) {
        $country_languages = array(
            'GH' => array('tw', 'en'),
            'KE' => array('sw', 'en'),
            'NG' => array('yo', 'en'),
            'ZA' => array('zu', 'en')
        );
        
        $likely = $country_languages[$country_code] ?? array('en');
        
        // Remove current language
        $likely = array_filter($likely, function($lang) use ($current_language) {
            return $lang !== $current_language;
        });
        
        return array_slice($likely, 0, 2); // Limit to 2 languages for performance
    }
    
    /**
     * Start output buffering for faster switching
     */
    public function start_output_buffering() {
        if (!is_admin() && !wp_doing_ajax()) {
            ob_start();
        }
    }
    
    /**
     * Preload critical languages
     */
    public function preload_critical_languages() {
        if (get_option('chatgabi_preload_enabled', true)) {
            $user_country = chatgabi_get_user_country();
            $current_language = chatgabi_get_user_preferred_language();
            $likely_languages = $this->get_likely_languages($user_country, $current_language);
            
            // Schedule background preloading
            wp_schedule_single_event(time() + 5, 'chatgabi_preload_languages', array($likely_languages, $user_country));
        }
    }
    
    /**
     * Background preload languages
     */
    public function background_preload_languages($languages, $country_code) {
        foreach ($languages as $language) {
            $this->preload_manager->preload_language_data($language, $country_code);
        }
    }
    
    /**
     * Get language switch analytics
     */
    public function ajax_get_switch_analytics() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'chatgabi_analytics_nonce')) {
            wp_send_json_error(__('Security check failed', 'chatgabi'));
        }
        
        $analytics = $this->switch_analytics->get_switch_analytics(get_current_user_id());
        wp_send_json_success($analytics);
    }
}

/**
 * Language Cache Manager
 */
class ChatGABI_Language_Cache {
    
    private $cache_prefix = 'chatgabi_lang_cache_';
    
    public function get_language_data($language, $country_code) {
        $cache_key = $this->cache_prefix . $language . '_' . $country_code;
        return get_transient($cache_key);
    }
    
    public function set_language_data($language, $country_code, $data) {
        $cache_key = $this->cache_prefix . $language . '_' . $country_code;
        // Cache for 6 hours
        return set_transient($cache_key, $data, 6 * HOUR_IN_SECONDS);
    }
    
    public function clear_language_cache($language = null, $country_code = null) {
        if ($language && $country_code) {
            $cache_key = $this->cache_prefix . $language . '_' . $country_code;
            delete_transient($cache_key);
        } else {
            // Clear all language cache
            global $wpdb;
            $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_{$this->cache_prefix}%'");
        }
    }
}

/**
 * Language Switch Analytics
 */
class ChatGABI_Language_Switch_Analytics {
    
    public function log_language_switch($data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_language_switch_analytics';
        
        // Create table if not exists
        $this->create_analytics_table();
        
        return $wpdb->insert($table_name, array_merge(array(
            'created_at' => current_time('mysql')
        ), $data));
    }
    
    public function get_switch_analytics($user_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_language_switch_analytics';
        
        return $wpdb->get_results($wpdb->prepare("
            SELECT 
                from_language,
                to_language,
                COUNT(*) as switch_count,
                AVG(processing_time) as avg_processing_time,
                SUM(cache_hit) as cache_hits
            FROM {$table_name}
            WHERE user_id = %d
            AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY from_language, to_language
            ORDER BY switch_count DESC
        ", $user_id));
    }
    
    private function create_analytics_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_language_switch_analytics';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20),
            from_language varchar(10) NOT NULL,
            to_language varchar(10) NOT NULL,
            processing_time decimal(10,2) NOT NULL,
            cache_hit tinyint(1) DEFAULT 0,
            context text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY languages (from_language, to_language),
            KEY created_at (created_at)
        ) {$charset_collate};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

/**
 * Language Preload Manager
 */
class ChatGABI_Language_Preload_Manager {
    
    public function preload_language_data($language, $country_code) {
        $start_time = microtime(true);
        
        try {
            // Get AI translation instance
            $ai_translation = chatgabi_get_ai_translation();
            
            // Preload UI strings
            $ui_strings = chatgabi_get_language_strings($language);
            
            // Preload cultural context
            $cultural_enhancement = chatgabi_get_african_cultural_enhancement();
            $cultural_context = $cultural_enhancement->get_cultural_context($country_code, 'preload');
            
            // Cache the preloaded data
            $cache_manager = new ChatGABI_Language_Cache();
            $cache_data = array(
                'ui_strings' => $ui_strings,
                'cultural_context' => $cultural_context,
                'preloaded_at' => time()
            );
            
            $cache_manager->set_language_data($language, $country_code, $cache_data);
            
            return array(
                'success' => true,
                'processing_time' => (microtime(true) - $start_time) * 1000,
                'language' => $language,
                'country_code' => $country_code
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'processing_time' => (microtime(true) - $start_time) * 1000
            );
        }
    }
}

// Initialize Real-time Language Switching
function chatgabi_get_realtime_language_switching() {
    static $realtime_switching = null;
    
    if ($realtime_switching === null) {
        $realtime_switching = new ChatGABI_Realtime_Language_Switching();
    }
    
    return $realtime_switching;
}

// Initialize on WordPress init
add_action('init', function() {
    chatgabi_get_realtime_language_switching();
});
?>
