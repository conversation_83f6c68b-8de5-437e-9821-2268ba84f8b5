<?php
/**
 * Database Optimization for ChatGABI
 * 
 * Handles database indexes, query optimization, and caching
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Database Optimization Manager
 */
class ChatGABI_Database_Optimizer {
    
    private static $instance = null;
    private $query_cache = array();
    private $performance_stats = array();
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
        $this->init_performance_tracking();
    }

    /**
     * Initialize query optimization
     */
    public function init_query_optimization() {
        // Register query optimizations
        $this->register_query_optimizations();

        // Initialize performance tracking
        $this->init_performance_tracking();

        // Set up database indexes
        $this->ensure_database_indexes();

        return true;
    }

    /**
     * Get database statistics
     */
    public function get_database_stats() {
        global $wpdb;

        $stats = array(
            'total_queries' => 0,
            'slow_queries' => 0,
            'cache_hits' => 0,
            'cache_misses' => 0,
            'avg_query_time' => 0,
            'total_query_time' => 0
        );

        // Get performance stats from transient
        $performance_stats = get_transient('chatgabi_performance_stats');
        if ($performance_stats) {
            $stats = array_merge($stats, $performance_stats);
        }

        // Calculate cache hit rate
        $total_cache_requests = $stats['cache_hits'] + $stats['cache_misses'];
        $stats['cache_hit_rate'] = $total_cache_requests > 0 ?
            ($stats['cache_hits'] / $total_cache_requests) * 100 : 0;

        return $stats;
    }

    /**
     * Get database efficiency score
     */
    public function get_efficiency_score() {
        $stats = $this->get_database_stats();

        $score = 100;

        // Reduce score based on slow queries
        if ($stats['total_queries'] > 0) {
            $slow_query_percentage = ($stats['slow_queries'] / $stats['total_queries']) * 100;
            $score -= $slow_query_percentage * 2; // Penalize slow queries heavily
        }

        // Boost score based on cache hit rate
        $score += ($stats['cache_hit_rate'] - 50) * 0.5; // Bonus for good cache performance

        // Penalize high average query time
        if ($stats['avg_query_time'] > 100) { // 100ms threshold
            $score -= ($stats['avg_query_time'] - 100) * 0.1;
        }

        return max(0, min(100, round($score, 2)));
    }

    /**
     * Optimize database
     */
    public function optimize_database() {
        $results = array();

        // Run database maintenance
        $results['maintenance'] = $this->run_database_maintenance();

        // Clear old cache
        $results['cache_cleared'] = $this->clear_query_cache();

        // Optimize indexes
        $results['indexes_optimized'] = $this->optimize_database_indexes();

        // Update performance stats
        $results['stats_updated'] = $this->update_performance_stats();

        return $results;
    }

    /**
     * Cleanup old data
     */
    public function cleanup_old_data() {
        global $wpdb;

        $cleanup_results = array();

        // Clean up old chat logs (older than 6 months)
        $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
        if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $chat_logs_table))) {
            $deleted = $wpdb->query($wpdb->prepare(
                "DELETE FROM `{$chat_logs_table}` WHERE created_at < %s",
                date('Y-m-d H:i:s', strtotime('-6 months'))
            ));
            $cleanup_results['old_chat_logs'] = $deleted;
        }

        // Clean up old analytics data (older than 1 year)
        $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
        if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $analytics_table))) {
            $deleted = $wpdb->query($wpdb->prepare(
                "DELETE FROM `{$analytics_table}` WHERE created_at < %s",
                date('Y-m-d H:i:s', strtotime('-1 year'))
            ));
            $cleanup_results['old_analytics'] = $deleted;
        }

        // Clean up expired transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%' AND option_value < UNIX_TIMESTAMP()");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' AND option_name NOT IN (SELECT CONCAT('_transient_', SUBSTRING(option_name, 19)) FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_%')");

        $cleanup_results['expired_transients'] = true;

        return $cleanup_results;
    }

    /**
     * Ensure meta index exists
     */
    public function ensure_meta_index($meta_key) {
        global $wpdb;

        $index_name = 'idx_meta_key_' . sanitize_key($meta_key);
        $table_name = $wpdb->postmeta;

        // Check if index exists
        $index_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
             WHERE table_schema = %s AND table_name = %s AND index_name = %s",
            DB_NAME,
            $table_name,
            $index_name
        ));

        if (!$index_exists) {
            // Create index - Note: Cannot use prepare() for DDL statements with dynamic table/index names
            $sql = "ALTER TABLE `{$table_name}` ADD INDEX `{$index_name}` (meta_key(191), meta_value(191))";
            $wpdb->query($sql);
        }

        return true;
    }

    /**
     * Ensure database indexes are created
     */
    private function ensure_database_indexes() {
        global $wpdb;

        // Common meta keys that need indexes
        $meta_keys = array(
            'businesscraft_ai_country',
            'businesscraft_ai_industry',
            'chatgabi_preferred_language',
            'chatgabi_user_credits',
            'chatgabi_last_activity'
        );

        foreach ($meta_keys as $meta_key) {
            $this->ensure_meta_index($meta_key);
        }

        return true;
    }

    /**
     * Optimize database indexes
     */
    private function optimize_database_indexes() {
        global $wpdb;

        $tables = array(
            $wpdb->prefix . 'chatgabi_prompt_templates',
            $wpdb->prefix . 'businesscraft_ai_chat_logs',
            $wpdb->prefix . 'businesscraft_ai_transactions',
            $wpdb->prefix . 'chatgabi_feedback'
        );

        $optimized = 0;
        foreach ($tables as $table) {
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table}'")) {
                $result = $wpdb->query("OPTIMIZE TABLE {$table}");
                if ($result !== false) {
                    $optimized++;
                }
            }
        }

        return $optimized;
    }

    /**
     * Update performance statistics
     */
    private function update_performance_stats() {
        $stats = array(
            'last_optimization' => time(),
            'total_queries' => $this->performance_stats['total_queries'] ?? 0,
            'slow_queries' => $this->performance_stats['slow_queries'] ?? 0,
            'cache_hits' => $this->performance_stats['cache_hits'] ?? 0,
            'cache_misses' => $this->performance_stats['cache_misses'] ?? 0,
            'avg_query_time' => $this->performance_stats['avg_query_time'] ?? 0
        );

        set_transient('chatgabi_performance_stats', $stats, 3600);
        return true;
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('after_switch_theme', array($this, 'create_database_indexes'));
        add_action('wp_footer', array($this, 'log_performance_stats'));
        add_action('admin_footer', array($this, 'log_performance_stats'));
        
        // Query optimization hooks
        add_filter('pre_get_posts', array($this, 'optimize_post_queries'));
        add_action('init', array($this, 'register_query_optimizations'));
    }
    
    /**
     * Initialize performance tracking
     */
    private function init_performance_tracking() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->performance_stats['start_time'] = microtime(true);
            $this->performance_stats['start_memory'] = memory_get_usage();
            $this->performance_stats['queries'] = array();
        }
    }
    
    /**
     * Create database indexes for optimal performance
     */
    public function create_database_indexes() {
        global $wpdb;
        
        $indexes = array(
            // ChatGABI Templates table indexes
            array(
                'table' => $wpdb->prefix . 'chatgabi_prompt_templates',
                'indexes' => array(
                    'idx_user_public' => 'INDEX (user_id, is_public)',
                    'idx_category_status' => 'INDEX (category_id, status)',
                    'idx_language_sector' => 'INDEX (language_code, sector)',
                    'idx_created_featured' => 'INDEX (created_at, is_featured)',
                    'idx_usage_rating' => 'INDEX (usage_count, rating_average)',
                    'idx_search_title' => 'FULLTEXT (title, description)',
                    'idx_tags' => 'INDEX (tags(100))'
                )
            ),
            
            // Template Categories table indexes
            array(
                'table' => $wpdb->prefix . 'chatgabi_template_categories',
                'indexes' => array(
                    'idx_slug_status' => 'INDEX (slug, status)',
                    'idx_sort_status' => 'INDEX (sort_order, status)'
                )
            ),
            
            // Chat Logs table indexes
            array(
                'table' => $wpdb->prefix . 'businesscraft_ai_chat_logs',
                'indexes' => array(
                    'idx_user_created' => 'INDEX (user_id, created_at)',
                    'idx_language_context' => 'INDEX (language, context)',
                    'idx_model_tokens' => 'INDEX (model, tokens_used)',
                    'idx_message_hash' => 'INDEX (message_hash)'
                )
            ),
            
            // Credit Transactions table indexes
            array(
                'table' => $wpdb->prefix . 'businesscraft_ai_transactions',
                'indexes' => array(
                    'idx_user_status' => 'INDEX (user_id, status)',
                    'idx_reference_status' => 'INDEX (reference, status)',
                    'idx_created_amount' => 'INDEX (created_at, amount)',
                    'idx_package_currency' => 'INDEX (package, currency)'
                )
            ),
            
            // Credit Logs table indexes
            array(
                'table' => $wpdb->prefix . 'businesscraft_ai_credit_logs',
                'indexes' => array(
                    'idx_user_action' => 'INDEX (user_id, action)',
                    'idx_created_action' => 'INDEX (created_at, action)',
                    'idx_transaction_ref' => 'INDEX (transaction_reference)'
                )
            ),
            
            // Analytics table indexes
            array(
                'table' => $wpdb->prefix . 'businesscraft_ai_analytics',
                'indexes' => array(
                    'idx_user_event' => 'INDEX (user_id, event_type)',
                    'idx_session_created' => 'INDEX (session_id, created_at)',
                    'idx_event_created' => 'INDEX (event_type, created_at)',
                    'idx_ip_created' => 'INDEX (ip_address, created_at)'
                )
            ),
            
            // Feedback table indexes
            array(
                'table' => $wpdb->prefix . 'chatgabi_feedback',
                'indexes' => array(
                    'idx_user_rating' => 'INDEX (user_id, rating_score)',
                    'idx_conversation_rating' => 'INDEX (conversation_id, rating_score)',
                    'idx_country_sector' => 'INDEX (user_country, user_sector)',
                    'idx_created_training' => 'INDEX (created_at, is_training_data)',
                    'idx_rating_type' => 'INDEX (rating_type, rating_score)'
                )
            ),
            
            // Sector Logs table indexes  
            array(
                'table' => $wpdb->prefix . 'chatgabi_sector_logs',
                'indexes' => array(
                    'idx_user_timestamp' => 'INDEX (user_id, timestamp)',
                    'idx_country_sector' => 'INDEX (country, detected_sector)',
                    'idx_timestamp_sector' => 'INDEX (timestamp, detected_sector)',
                    'idx_opportunities' => 'INDEX (opportunities_included)'
                )
            )
        );
        
        foreach ($indexes as $table_config) {
            $this->create_table_indexes($table_config['table'], $table_config['indexes']);
        }
        
        // Log completion
        error_log('ChatGABI: Database indexes created successfully');
    }
    
    /**
     * Create indexes for a specific table
     */
    private function create_table_indexes($table, $indexes) {
        global $wpdb;
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table));
        if (!$table_exists) {
            return false;
        }
        
        foreach ($indexes as $index_name => $index_definition) {
            // Check if index already exists
            $existing_index = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                 WHERE TABLE_SCHEMA = DATABASE() 
                 AND TABLE_NAME = %s 
                 AND INDEX_NAME = %s",
                str_replace($wpdb->prefix, '', $table),
                $index_name
            ));
            
            if (!$existing_index) {
                $sql = "ALTER TABLE {$table} ADD {$index_definition}";
                $result = $wpdb->query($sql);
                
                if ($result === false) {
                    error_log("ChatGABI: Failed to create index {$index_name} on table {$table}");
                } else {
                    error_log("ChatGABI: Created index {$index_name} on table {$table}");
                }
            }
        }
    }
    
    /**
     * Optimize template queries to prevent N+1 issues
     */
    public function get_optimized_templates($params = array()) {
        global $wpdb;
        
        $defaults = array(
            'category' => null,
            'language' => null,
            'search' => null,
            'user_only' => false,
            'user_id' => get_current_user_id(),
            'limit' => 50,
            'offset' => 0
        );
        
        $params = wp_parse_args($params, $defaults);
        
        // Build cache key
        $cache_key = 'chatgabi_templates_' . md5(serialize($params));
        
        // Try to get from cache first
        $cached_result = $this->get_query_cache($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        $start_time = microtime(true);
        
        // Build optimized query with all necessary joins
        $templates_table = $wpdb->prefix . 'chatgabi_prompt_templates';
        $categories_table = $wpdb->prefix . 'chatgabi_template_categories';
        
        $where_conditions = array("t.status = 'active'");
        $where_values = array();
        
        // User filter
        if ($params['user_only'] && $params['user_id'] > 0) {
            $where_conditions[] = "t.user_id = %d";
            $where_values[] = $params['user_id'];
        } else {
            if ($params['user_id'] > 0) {
                $where_conditions[] = "(t.is_public = 1 OR t.user_id = %d)";
                $where_values[] = $params['user_id'];
            } else {
                $where_conditions[] = "t.is_public = 1";
            }
        }
        
        // Category filter
        if ($params['category'] && $params['category'] !== 'all') {
            $where_conditions[] = "c.slug = %s";
            $where_values[] = $params['category'];
        }
        
        // Language filter
        if ($params['language']) {
            $where_conditions[] = "t.language_code = %s";
            $where_values[] = $params['language'];
        }
        
        // Search filter using FULLTEXT if available
        if ($params['search']) {
            $search_term = $wpdb->esc_like($params['search']);
            $where_conditions[] = "(MATCH(t.title, t.description) AGAINST(%s IN NATURAL LANGUAGE MODE) OR t.title LIKE %s OR t.description LIKE %s)";
            $where_values[] = $search_term;
            $where_values[] = '%' . $search_term . '%';
            $where_values[] = '%' . $search_term . '%';
        }
        
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        
        // Check which column exists in the table (prompt_text or prompt_content)
        $columns = $wpdb->get_results("SHOW COLUMNS FROM {$templates_table}");
        $prompt_column = 'prompt_text'; // default
        foreach ($columns as $column) {
            if ($column->Field === 'prompt_content') {
                $prompt_column = 'prompt_content';
                break;
            }
        }

        // Optimized query with specific field selection
        $query = "SELECT
                    t.id, t.title, t.description, t.{$prompt_column}, t.language_code,
                    t.tags, t.sector, t.usage_count, t.rating_average, t.rating_count,
                    t.is_public, t.user_id, t.created_at, t.updated_at,
                    c.id as category_id, c.name as category_name, c.slug as category_slug,
                    c.icon as category_icon, c.color as category_color
                  FROM {$templates_table} t
                  LEFT JOIN {$categories_table} c ON t.category_id = c.id
                  {$where_clause}
                  ORDER BY t.is_featured DESC, t.usage_count DESC, t.created_at DESC
                  LIMIT %d OFFSET %d";
        
        $where_values[] = $params['limit'];
        $where_values[] = $params['offset'];
        
        $prepared_query = $wpdb->prepare($query, $where_values);
        $templates = $wpdb->get_results($prepared_query);
        
        $query_time = microtime(true) - $start_time;
        
        // Process templates
        $processed_templates = array();
        if ($templates) {
            foreach ($templates as $template) {
                // Get prompt content from the correct column
                $prompt_content = isset($template->prompt_content) ? $template->prompt_content : $template->prompt_text;

                $processed_templates[] = array(
                    'id' => (int) $template->id,
                    'title' => $template->title,
                    'description' => $template->description,
                    'prompt_content' => $prompt_content, // Ensure consistent API response
                    'category' => array(
                        'id' => (int) $template->category_id,
                        'name' => $template->category_name ?: 'General',
                        'slug' => $template->category_slug ?: 'general',
                        'icon' => $template->category_icon ?: '📋',
                        'color' => $template->category_color ?: '#667eea'
                    ),
                    'language_code' => $template->language_code ?: 'en',
                    'tags' => $template->tags ? explode(',', $template->tags) : array(),
                    'sector' => $template->sector ?: 'General',
                    'usage_count' => (int) $template->usage_count,
                    'rating_average' => (float) $template->rating_average,
                    'rating_count' => (int) $template->rating_count,
                    'is_public' => (bool) $template->is_public,
                    'is_owner' => $params['user_id'] > 0 && $template->user_id == $params['user_id'],
                    'created_at' => $template->created_at
                );
            }
        }
        
        $result = array(
            'templates' => $processed_templates,
            'count' => count($processed_templates),
            'query_time' => $query_time
        );
        
        // Cache the result
        $this->set_query_cache($cache_key, $result);
        
        // Log performance
        $this->log_query_performance('get_templates', $query_time, count($processed_templates));
        
        return $result;
    }
    
    /**
     * Optimized chat history retrieval
     */
    public function get_optimized_chat_history($user_id, $limit = 10, $offset = 0) {
        global $wpdb;
        
        $cache_key = "chatgabi_chat_history_{$user_id}_{$limit}_{$offset}";
        
        // Try cache first
        $cached_result = $this->get_query_cache($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        $start_time = microtime(true);
        
        $table_name = $wpdb->prefix . 'businesscraft_ai_chat_logs';
        
        // Optimized query with specific field selection
        $sql = "SELECT id, encrypted_user_message, encrypted_ai_response,
                       language, model, tokens_used, credits_used, created_at
                FROM `{$table_name}`
                WHERE user_id = %d
                ORDER BY created_at DESC
                LIMIT %d OFFSET %d";

        $results = $wpdb->get_results($wpdb->prepare($sql, $user_id, $limit, $offset));
        
        $query_time = microtime(true) - $start_time;
        
        $chat_history = array();
        foreach ($results as $row) {
            $chat_history[] = array(
                'id' => $row->id,
                'user_message' => function_exists('businesscraft_ai_decrypt') ? businesscraft_ai_decrypt($row->encrypted_user_message) : $row->encrypted_user_message,
                'ai_response' => function_exists('businesscraft_ai_decrypt') ? businesscraft_ai_decrypt($row->encrypted_ai_response) : $row->encrypted_ai_response,
                'language' => $row->language,
                'model' => $row->model,
                'tokens_used' => $row->tokens_used,
                'credits_used' => $row->credits_used,
                'created_at' => $row->created_at,
            );
        }
        
        // Cache for 5 minutes
        $this->set_query_cache($cache_key, $chat_history, 300);
        
        $this->log_query_performance('get_chat_history', $query_time, count($chat_history));
        
        return $chat_history;
    }
    
    /**
     * Optimized analytics retrieval
     */
    public function get_optimized_analytics($date_from = null, $date_to = null) {
        global $wpdb;
        
        if (!$date_from) {
            $date_from = date('Y-m-d', strtotime('-30 days'));
        }
        if (!$date_to) {
            $date_to = date('Y-m-d');
        }
        
        $cache_key = "chatgabi_analytics_{$date_from}_{$date_to}";
        
        // Try cache first
        $cached_result = $this->get_query_cache($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        $start_time = microtime(true);
        
        $analytics_table = $wpdb->prefix . 'businesscraft_ai_analytics';
        $chat_logs_table = $wpdb->prefix . 'businesscraft_ai_chat_logs';
        $transactions_table = $wpdb->prefix . 'businesscraft_ai_transactions';
        
        // Use optimized queries with proper indexes
        $date_condition = "created_at >= %s AND created_at <= %s";
        $date_params = array($date_from, $date_to . ' 23:59:59');
        
        // Monthly Active Users (using index)
        $mau_sql = "SELECT COUNT(DISTINCT user_id) FROM `{$analytics_table}` WHERE {$date_condition}";
        $mau = $wpdb->get_var($wpdb->prepare($mau_sql, ...$date_params));

        // Total chats (using index)
        $chats_sql = "SELECT COUNT(*) FROM `{$chat_logs_table}` WHERE {$date_condition}";
        $total_chats = $wpdb->get_var($wpdb->prepare($chats_sql, ...$date_params));

        // Revenue (using index)
        $revenue_sql = "SELECT COALESCE(SUM(amount), 0) FROM `{$transactions_table}`
                        WHERE status = 'completed' AND {$date_condition}";
        $revenue = $wpdb->get_var($wpdb->prepare($revenue_sql, ...$date_params));

        // Average session length (optimized with proper grouping)
        $session_sql = "SELECT AVG(session_duration) FROM (
                           SELECT TIMESTAMPDIFF(MINUTE, MIN(created_at), MAX(created_at)) as session_duration
                           FROM `{$analytics_table}`
                           WHERE {$date_condition}
                           GROUP BY session_id
                           HAVING COUNT(*) > 1 AND session_duration > 0
                           LIMIT 1000
                       ) as session_stats";
        $avg_session_length = $wpdb->get_var($wpdb->prepare($session_sql, ...$date_params));
        
        $query_time = microtime(true) - $start_time;
        
        $result = array(
            'mau' => intval($mau),
            'total_chats' => intval($total_chats),
            'revenue' => floatval($revenue),
            'avg_session_length' => floatval($avg_session_length),
            'date_range' => array(
                'from' => $date_from,
                'to' => $date_to,
            ),
            'query_time' => $query_time
        );
        
        // Cache for 1 hour
        $this->set_query_cache($cache_key, $result, 3600);
        
        $this->log_query_performance('get_analytics', $query_time, 4);
        
        return $result;
    }
    
    /**
     * Query result caching
     */
    private function get_query_cache($key) {
        if (defined('CHATGABI_ENABLE_QUERY_CACHING') && CHATGABI_ENABLE_QUERY_CACHING) {
            return get_transient('chatgabi_query_' . md5($key));
        }
        return false;
    }
    
    /**
     * Set query cache
     */
    private function set_query_cache($key, $data, $expiration = 600) {
        if (defined('CHATGABI_ENABLE_QUERY_CACHING') && CHATGABI_ENABLE_QUERY_CACHING) {
            set_transient('chatgabi_query_' . md5($key), $data, $expiration);
        }
    }
    
    /**
     * Register query optimizations
     */
    public function register_query_optimizations() {
        // Override default template queries
        add_filter('chatgabi_get_templates_query', array($this, 'get_optimized_templates'));
        add_filter('businesscraft_ai_get_chat_history', array($this, 'get_optimized_chat_history'), 10, 3);
        add_filter('businesscraft_ai_get_analytics', array($this, 'get_optimized_analytics'), 10, 2);
    }
    
    /**
     * Optimize WordPress post queries
     */
    public function optimize_post_queries($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Limit post queries to improve performance
            if (is_home() || is_archive()) {
                $query->set('posts_per_page', 10);
                $query->set('no_found_rows', true);
            }
        }
        return $query;
    }
    
    /**
     * Log query performance
     */
    private function log_query_performance($query_type, $execution_time, $result_count) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $this->performance_stats['queries'][] = array(
                'type' => $query_type,
                'time' => $execution_time,
                'results' => $result_count,
                'timestamp' => current_time('mysql')
            );
            
            // Log slow queries
            if ($execution_time > 0.5) {
                error_log("ChatGABI: Slow query detected - {$query_type}: {$execution_time}s, {$result_count} results");
            }
        }
    }
    
    /**
     * Log performance statistics
     */
    public function log_performance_stats() {
        if (defined('WP_DEBUG') && WP_DEBUG && !empty($this->performance_stats['queries'])) {
            $total_time = microtime(true) - $this->performance_stats['start_time'];
            $memory_usage = memory_get_usage() - $this->performance_stats['start_memory'];
            $query_count = count($this->performance_stats['queries']);
            
            $stats = array(
                'total_time' => $total_time,
                'memory_usage' => $memory_usage,
                'query_count' => $query_count,
                'queries' => $this->performance_stats['queries']
            );
            
            // Store performance stats
            set_transient('chatgabi_performance_stats', $stats, 3600);
        }
    }
    
    /**
     * Get performance statistics
     */
    public function get_performance_stats() {
        return get_transient('chatgabi_performance_stats') ?: array();
    }
    
    /**
     * Clear query cache
     */
    public function clear_query_cache() {
        global $wpdb;
        
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_chatgabi_query_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_chatgabi_query_%'");
        
        return true;
    }
    
    /**
     * Database maintenance
     */
    public function run_database_maintenance() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'chatgabi_prompt_templates',
            $wpdb->prefix . 'chatgabi_template_categories', 
            $wpdb->prefix . 'businesscraft_ai_chat_logs',
            $wpdb->prefix . 'businesscraft_ai_transactions',
            $wpdb->prefix . 'businesscraft_ai_credit_logs',
            $wpdb->prefix . 'businesscraft_ai_analytics',
            $wpdb->prefix . 'chatgabi_feedback'
        );
        
        foreach ($tables as $table) {
            if ($wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table))) {
                $wpdb->query("OPTIMIZE TABLE `{$table}`");
            }
        }
        
        // Clear old transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_%' AND option_value = ''");
        
        return true;
    }
}

/**
 * Global helper functions
 */

/**
 * Get optimized templates
 */
function chatgabi_get_optimized_templates($params = array()) {
    $optimizer = ChatGABI_Database_Optimizer::get_instance();
    return $optimizer->get_optimized_templates($params);
}

/**
 * Get optimized chat history
 */
function chatgabi_get_optimized_chat_history($user_id, $limit = 10, $offset = 0) {
    $optimizer = ChatGABI_Database_Optimizer::get_instance();
    return $optimizer->get_optimized_chat_history($user_id, $limit, $offset);
}

/**
 * Get optimized analytics
 */
function chatgabi_get_optimized_analytics($date_from = null, $date_to = null) {
    $optimizer = ChatGABI_Database_Optimizer::get_instance();
    return $optimizer->get_optimized_analytics($date_from, $date_to);
}

/**
 * Clear query cache
 */
function chatgabi_clear_query_cache() {
    $optimizer = ChatGABI_Database_Optimizer::get_instance();
    return $optimizer->clear_query_cache();
}

/**
 * Run database maintenance
 */
function chatgabi_run_database_maintenance() {
    $optimizer = ChatGABI_Database_Optimizer::get_instance();
    return $optimizer->run_database_maintenance();
}

// Initialize the database optimizer
add_action('init', function() {
    ChatGABI_Database_Optimizer::get_instance();
});

// Schedule database maintenance
if (!wp_next_scheduled('chatgabi_database_maintenance')) {
    wp_schedule_event(time(), 'weekly', 'chatgabi_database_maintenance');
}

add_action('chatgabi_database_maintenance', 'chatgabi_run_database_maintenance');
