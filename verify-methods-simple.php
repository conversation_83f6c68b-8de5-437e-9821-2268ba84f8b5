<?php
/**
 * Simple verification of missing methods fix
 */

echo "Testing ChatGABI Missing Methods Fix...\n\n";

// Check if files exist and contain the new methods
$files_to_check = array(
    'wp-content/themes/businesscraft-ai/inc/database-optimization.php' => array(
        'init_query_optimization',
        'get_database_stats',
        'get_efficiency_score',
        'optimize_database',
        'cleanup_old_data',
        'ensure_meta_index'
    ),
    'wp-content/themes/businesscraft-ai/inc/asset-optimization.php' => array(
        'init_asset_optimization'
    ),
    'wp-content/themes/businesscraft-ai/inc/performance-monitor.php' => array(
        'start_monitoring',
        'get_response_times',
        'get_response_time_score',
        'log_page_performance'
    ),
    'wp-content/themes/businesscraft-ai/inc/memory-optimizer.php' => array(
        'init_memory_optimization',
        'get_efficiency_score',
        'optimize_memory',
        'cleanup_memory',
        'cleanup_temporary_data'
    )
);

$all_methods_found = true;

foreach ($files_to_check as $file => $methods) {
    echo "Checking {$file}:\n";
    
    if (!file_exists($file)) {
        echo "   ❌ File not found\n";
        $all_methods_found = false;
        continue;
    }
    
    $content = file_get_contents($file);
    
    foreach ($methods as $method) {
        if (strpos($content, "function {$method}(") !== false) {
            echo "   ✅ {$method}() method found\n";
        } else {
            echo "   ❌ {$method}() method missing\n";
            $all_methods_found = false;
        }
    }
    echo "\n";
}

// Check if the advanced performance optimization file calls these methods correctly
echo "Checking advanced-performance-optimization.php method calls:\n";
$advanced_file = 'wp-content/themes/businesscraft-ai/inc/advanced-performance-optimization.php';

if (file_exists($advanced_file)) {
    $content = file_get_contents($advanced_file);
    
    $method_calls = array(
        'init_query_optimization',
        'init_asset_optimization',
        'start_monitoring',
        'init_memory_optimization'
    );
    
    foreach ($method_calls as $method_call) {
        if (strpos($content, $method_call . '()') !== false) {
            echo "   ✅ {$method_call}() call found\n";
        } else {
            echo "   ❌ {$method_call}() call missing\n";
            $all_methods_found = false;
        }
    }
} else {
    echo "   ❌ Advanced performance optimization file not found\n";
    $all_methods_found = false;
}

echo "\n" . str_repeat("=", 50) . "\n";

if ($all_methods_found) {
    echo "🎉 SUCCESS! All missing methods have been implemented.\n";
    echo "✅ The fatal error should be resolved.\n";
    echo "✅ WordPress should load without issues.\n";
    echo "✅ ChatGABI performance optimization system should work correctly.\n";
} else {
    echo "❌ Some methods are still missing.\n";
    echo "Please review the implementation and ensure all methods are properly added.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
?>
