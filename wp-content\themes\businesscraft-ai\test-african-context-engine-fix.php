<?php
/**
 * Test African Context Engine Class Loading Fix
 * 
 * This script tests that the BusinessCraft_African_Context_Engine class
 * is properly loaded and accessible to the enhanced template translator.
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die("❌ Could not load WordPress\n");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>ChatGABI African Context Engine Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .pass { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .fail { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; margin: 10px 0; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h1 { color: #333; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 5px; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🌍 ChatGABI African Context Engine Class Loading Fix Test</h1>
    
    <div class="test-section">
        <h2>1. Class Existence Check</h2>
        <?php
        $african_context_class_exists = class_exists('BusinessCraft_African_Context_Engine');
        ?>
        <div class="test-result <?php echo $african_context_class_exists ? 'pass' : 'fail'; ?>">
            <?php echo $african_context_class_exists ? '✅' : '❌'; ?> 
            <code>BusinessCraft_African_Context_Engine</code> class 
            <?php echo $african_context_class_exists ? 'exists' : 'not found'; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. Class Instantiation Test</h2>
        <?php
        $instantiation_success = false;
        $instantiation_error = '';
        
        if ($african_context_class_exists) {
            try {
                $african_context = new BusinessCraft_African_Context_Engine();
                $instantiation_success = true;
            } catch (Exception $e) {
                $instantiation_error = $e->getMessage();
            }
        }
        ?>
        <div class="test-result <?php echo $instantiation_success ? 'pass' : 'fail'; ?>">
            <?php echo $instantiation_success ? '✅' : '❌'; ?> 
            Class instantiation <?php echo $instantiation_success ? 'successful' : 'failed'; ?>
            <?php if ($instantiation_error): ?>
                <br><small>Error: <?php echo esc_html($instantiation_error); ?></small>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Enhanced Template Translator Test</h2>
        <?php
        $translator_class_exists = class_exists('ChatGABI_Enhanced_Template_Translator');
        $translator_instantiation_success = false;
        $translator_error = '';
        
        if ($translator_class_exists) {
            try {
                $translator = new ChatGABI_Enhanced_Template_Translator();
                $translator_instantiation_success = true;
            } catch (Exception $e) {
                $translator_error = $e->getMessage();
            }
        }
        ?>
        <div class="test-result <?php echo $translator_class_exists ? 'pass' : 'fail'; ?>">
            <?php echo $translator_class_exists ? '✅' : '❌'; ?> 
            <code>ChatGABI_Enhanced_Template_Translator</code> class 
            <?php echo $translator_class_exists ? 'exists' : 'not found'; ?>
        </div>
        
        <div class="test-result <?php echo $translator_instantiation_success ? 'pass' : 'fail'; ?>">
            <?php echo $translator_instantiation_success ? '✅' : '❌'; ?> 
            Enhanced Template Translator instantiation <?php echo $translator_instantiation_success ? 'successful' : 'failed'; ?>
            <?php if ($translator_error): ?>
                <br><small>Error: <?php echo esc_html($translator_error); ?></small>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. Functionality Test</h2>
        <?php
        $functionality_test_passed = false;
        $functionality_error = '';
        
        if ($instantiation_success && $translator_instantiation_success) {
            try {
                // Test African Context Engine functionality
                $country_context = $african_context->get_country_context('GH');
                $market_examples = $african_context->generate_market_examples('GH', 'general');
                
                // Test Enhanced Template Translator functionality
                $test_content = "This is a test business template for entrepreneurs in Ghana.";
                $translated_content = $translator->translate_template($test_content, 'en', 'GH', 'technology');
                
                $functionality_test_passed = !empty($country_context) && !empty($market_examples) && !empty($translated_content);
                
            } catch (Exception $e) {
                $functionality_error = $e->getMessage();
            }
        }
        ?>
        <div class="test-result <?php echo $functionality_test_passed ? 'pass' : 'fail'; ?>">
            <?php echo $functionality_test_passed ? '✅' : '❌'; ?> 
            Functionality test <?php echo $functionality_test_passed ? 'passed' : 'failed'; ?>
            <?php if ($functionality_error): ?>
                <br><small>Error: <?php echo esc_html($functionality_error); ?></small>
            <?php endif; ?>
        </div>
        
        <?php if ($functionality_test_passed): ?>
            <div class="info">
                <strong>Sample Output:</strong>
                <ul>
                    <li><strong>Country Context:</strong> <?php echo esc_html($country_context['business_culture'] ?? 'N/A'); ?></li>
                    <li><strong>Market Examples:</strong> <?php echo esc_html(implode(', ', array_slice($market_examples['successful_businesses']['tech_startups'] ?? ['N/A'], 0, 3))); ?></li>
                    <li><strong>Translated Content Length:</strong> <?php echo strlen($translated_content); ?> characters</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>5. Priority 3 Integration Test</h2>
        <?php
        $priority3_class_exists = class_exists('ChatGABI_Priority3_Integration');
        $priority3_instantiation_success = false;
        $priority3_error = '';
        
        if ($priority3_class_exists) {
            try {
                $priority3 = new ChatGABI_Priority3_Integration();
                $priority3_instantiation_success = true;
            } catch (Exception $e) {
                $priority3_error = $e->getMessage();
            }
        }
        ?>
        <div class="test-result <?php echo $priority3_class_exists ? 'pass' : 'fail'; ?>">
            <?php echo $priority3_class_exists ? '✅' : '❌'; ?> 
            <code>ChatGABI_Priority3_Integration</code> class 
            <?php echo $priority3_class_exists ? 'exists' : 'not found'; ?>
        </div>
        
        <div class="test-result <?php echo $priority3_instantiation_success ? 'pass' : 'fail'; ?>">
            <?php echo $priority3_instantiation_success ? '✅' : '❌'; ?> 
            Priority 3 Integration instantiation <?php echo $priority3_instantiation_success ? 'successful' : 'failed'; ?>
            <?php if ($priority3_error): ?>
                <br><small>Error: <?php echo esc_html($priority3_error); ?></small>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="test-section">
        <h2>6. File Loading Order Verification</h2>
        <?php
        $functions_file = get_template_directory() . '/functions.php';
        $functions_content = file_get_contents($functions_file);
        
        $african_context_loaded = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/african-context-engine.php';") !== false;
        $enhanced_translator_loaded = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/enhanced-template-translator.php';") !== false;
        
        // Check if african-context-engine.php comes before enhanced-template-translator.php
        $african_context_pos = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/african-context-engine.php';");
        $enhanced_translator_pos = strpos($functions_content, "require_once CHATGABI_THEME_DIR . '/inc/enhanced-template-translator.php';");
        
        $correct_loading_order = $african_context_loaded && $enhanced_translator_loaded && ($african_context_pos < $enhanced_translator_pos);
        ?>
        <div class="test-result <?php echo $african_context_loaded ? 'pass' : 'fail'; ?>">
            <?php echo $african_context_loaded ? '✅' : '❌'; ?> 
            African Context Engine file included in functions.php
        </div>
        
        <div class="test-result <?php echo $enhanced_translator_loaded ? 'pass' : 'fail'; ?>">
            <?php echo $enhanced_translator_loaded ? '✅' : '❌'; ?> 
            Enhanced Template Translator file included in functions.php
        </div>
        
        <div class="test-result <?php echo $correct_loading_order ? 'pass' : 'fail'; ?>">
            <?php echo $correct_loading_order ? '✅' : '❌'; ?> 
            Correct loading order (African Context Engine before Enhanced Template Translator)
        </div>
    </div>
    
    <div class="test-section">
        <h2>7. Overall Status</h2>
        <?php
        $all_tests_passed = $african_context_class_exists && $instantiation_success && 
                           $translator_class_exists && $translator_instantiation_success && 
                           $functionality_test_passed && $priority3_class_exists && 
                           $priority3_instantiation_success && $correct_loading_order;
        ?>
        
        <div class="test-result <?php echo $all_tests_passed ? 'pass' : 'fail'; ?>">
            <?php echo $all_tests_passed ? '✅' : '❌'; ?> 
            <strong><?php echo $all_tests_passed ? 'SUCCESS' : 'ISSUES DETECTED'; ?>:</strong> 
            African Context Engine dependency <?php echo $all_tests_passed ? 'resolved' : 'needs attention'; ?>
        </div>
        
        <?php if ($all_tests_passed): ?>
            <div class="info">
                <strong>Resolution Summary:</strong>
                <ul>
                    <li>African Context Engine class properly loaded</li>
                    <li>Enhanced Template Translator can instantiate successfully</li>
                    <li>Priority 3 Integration system working</li>
                    <li>Correct file loading order established</li>
                    <li>All African market customization features functional</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="info">
        <strong>Next Steps:</strong>
        <ul>
            <li>Test the ChatGABI template interface to ensure African context features work</li>
            <li>Verify that country-specific business examples are displayed</li>
            <li>Check that cultural context is properly applied to templates</li>
            <li>Monitor error logs for any remaining dependency issues</li>
        </ul>
    </div>
</body>
</html>
