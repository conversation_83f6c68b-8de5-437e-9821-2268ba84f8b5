<?php
/**
 * Simple test for ChatGABI Performance Optimization Fix
 */

// Basic WordPress setup
define('ABSPATH', __DIR__ . '/');
define('WP_DEBUG', true);

echo "Testing ChatGABI Performance Optimization Fix...\n\n";

// Check if the file exists and contains the fix
$file_path = 'wp-content/themes/businesscraft-ai/inc/advanced-performance-optimization.php';

if (!file_exists($file_path)) {
    echo "❌ File not found: {$file_path}\n";
    exit(1);
}

$content = file_get_contents($file_path);

// Check for the old problematic code
$old_patterns = array(
    'new ChatGABI_Database_Optimizer()',
    'new ChatGABI_Asset_Optimizer()',
    'new ChatGABI_Performance_Monitor()',
    'new ChatGABI_Memory_Optimizer()'
);

// Check for the new correct code
$new_patterns = array(
    'ChatGABI_Database_Optimizer::get_instance()',
    'ChatGABI_Asset_Optimizer::get_instance()',
    'ChatGABI_Performance_Monitor::get_instance()',
    'ChatGABI_Memory_Optimizer::get_instance()'
);

echo "1. Checking for old problematic code:\n";
$old_found = false;
foreach ($old_patterns as $pattern) {
    if (strpos($content, $pattern) !== false) {
        echo "   ❌ Found old code: {$pattern}\n";
        $old_found = true;
    } else {
        echo "   ✅ Old code removed: {$pattern}\n";
    }
}

echo "\n2. Checking for new correct code:\n";
$new_found = 0;
foreach ($new_patterns as $pattern) {
    if (strpos($content, $pattern) !== false) {
        echo "   ✅ Found new code: {$pattern}\n";
        $new_found++;
    } else {
        echo "   ❌ Missing new code: {$pattern}\n";
    }
}

echo "\n3. Summary:\n";
if (!$old_found && $new_found === count($new_patterns)) {
    echo "   🎉 SUCCESS! All singleton instantiation issues have been fixed.\n";
    echo "   ✅ The fatal error should be resolved.\n";
    echo "   ✅ All classes are now properly instantiated using get_instance() methods.\n";
} else {
    echo "   ❌ ISSUES REMAIN:\n";
    if ($old_found) {
        echo "      • Old problematic code still exists\n";
    }
    if ($new_found !== count($new_patterns)) {
        echo "      • Not all singleton patterns have been implemented\n";
    }
}

echo "\n4. Code snippet verification:\n";
// Extract the constructor method to verify the fix
$constructor_start = strpos($content, 'public function __construct() {');
if ($constructor_start !== false) {
    $constructor_end = strpos($content, '}', $constructor_start);
    $constructor_code = substr($content, $constructor_start, $constructor_end - $constructor_start + 1);
    
    echo "   Constructor code found:\n";
    echo "   " . str_replace("\n", "\n   ", trim($constructor_code)) . "\n";
    
    // Verify the specific lines are correct
    if (strpos($constructor_code, 'ChatGABI_Database_Optimizer::get_instance()') !== false &&
        strpos($constructor_code, 'ChatGABI_Asset_Optimizer::get_instance()') !== false &&
        strpos($constructor_code, 'ChatGABI_Performance_Monitor::get_instance()') !== false &&
        strpos($constructor_code, 'ChatGABI_Memory_Optimizer::get_instance()') !== false) {
        echo "\n   ✅ Constructor properly uses singleton pattern for all classes!\n";
    } else {
        echo "\n   ❌ Constructor still has issues with singleton instantiation.\n";
    }
} else {
    echo "   ❌ Constructor method not found in the file.\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "Fix verification complete.\n";
?>
