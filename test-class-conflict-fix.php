<?php
/**
 * Test script to verify ChatGABI class name conflict fix
 * 
 * This script tests that the ChatGABI_Performance_Monitor class conflict
 * has been resolved by renaming the scraping performance monitor class.
 */

// WordPress environment setup
define('WP_USE_THEMES', false);
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>ChatGABI Class Name Conflict Fix Test</h1>\n";
echo "<style>body{font-family:Arial,sans-serif;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

$tests_passed = 0;
$total_tests = 0;

// Test 1: Check if main ChatGABI_Performance_Monitor class exists
echo "<h2>Test 1: Main Performance Monitor Class</h2>\n";
$total_tests++;

if (class_exists('ChatGABI_Performance_Monitor')) {
    echo "<div class='success'>✅ ChatGABI_Performance_Monitor class exists (main performance monitor)</div>\n";
    
    // Check if it's the singleton version (main class)
    $reflection = new ReflectionClass('ChatGABI_Performance_Monitor');
    if ($reflection->hasMethod('get_instance')) {
        echo "<div class='success'>✅ Main performance monitor has singleton pattern (get_instance method)</div>\n";
        $tests_passed++;
    } else {
        echo "<div class='error'>❌ Main performance monitor missing singleton pattern</div>\n";
    }
} else {
    echo "<div class='error'>❌ ChatGABI_Performance_Monitor class not found</div>\n";
}

// Test 2: Check if renamed scraping performance monitor class exists
echo "<h2>Test 2: Scraping Performance Monitor Class</h2>\n";
$total_tests++;

if (class_exists('ChatGABI_Scraping_Performance_Monitor')) {
    echo "<div class='success'>✅ ChatGABI_Scraping_Performance_Monitor class exists (renamed scraping monitor)</div>\n";
    
    // Check if it has scraping-specific methods
    $reflection = new ReflectionClass('ChatGABI_Scraping_Performance_Monitor');
    if ($reflection->hasMethod('start_cycle') && $reflection->hasMethod('end_cycle')) {
        echo "<div class='success'>✅ Scraping performance monitor has expected methods (start_cycle, end_cycle)</div>\n";
        $tests_passed++;
    } else {
        echo "<div class='error'>❌ Scraping performance monitor missing expected methods</div>\n";
    }
} else {
    echo "<div class='error'>❌ ChatGABI_Scraping_Performance_Monitor class not found</div>\n";
}

// Test 3: Test instantiation of both classes
echo "<h2>Test 3: Class Instantiation Test</h2>\n";
$total_tests++;

try {
    // Test main performance monitor
    $main_monitor = ChatGABI_Performance_Monitor::get_instance();
    echo "<div class='success'>✅ Main performance monitor instantiated successfully</div>\n";
    
    // Test scraping performance monitor
    $scraping_monitor = new ChatGABI_Scraping_Performance_Monitor();
    echo "<div class='success'>✅ Scraping performance monitor instantiated successfully</div>\n";
    
    $tests_passed++;
} catch (Exception $e) {
    echo "<div class='error'>❌ Error instantiating classes: " . $e->getMessage() . "</div>\n";
}

// Test 4: Check if advanced web scraper can use the renamed class
echo "<h2>Test 4: Advanced Web Scraper Integration</h2>\n";
$total_tests++;

if (class_exists('ChatGABI_Advanced_Web_Scraper')) {
    try {
        // This should not cause a fatal error anymore
        $scraper = new ChatGABI_Advanced_Web_Scraper();
        echo "<div class='success'>✅ ChatGABI_Advanced_Web_Scraper instantiated without class conflict</div>\n";
        $tests_passed++;
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error instantiating advanced web scraper: " . $e->getMessage() . "</div>\n";
    }
} else {
    echo "<div class='info'>ℹ️ ChatGABI_Advanced_Web_Scraper class not found (may not be loaded)</div>\n";
    $tests_passed++; // Don't penalize if class isn't loaded
}

// Test 5: Verify no duplicate class declarations
echo "<h2>Test 5: Duplicate Class Declaration Check</h2>\n";
$total_tests++;

// Check if we can include both files without fatal error
$performance_monitor_file = 'wp-content/themes/businesscraft-ai/inc/performance-monitor.php';
$scraping_infrastructure_file = 'wp-content/themes/businesscraft-ai/inc/scraping-infrastructure.php';

$files_exist = true;
if (!file_exists($performance_monitor_file)) {
    echo "<div class='error'>❌ Performance monitor file not found</div>\n";
    $files_exist = false;
}

if (!file_exists($scraping_infrastructure_file)) {
    echo "<div class='error'>❌ Scraping infrastructure file not found</div>\n";
    $files_exist = false;
}

if ($files_exist) {
    // Check file contents for class declarations
    $perf_content = file_get_contents($performance_monitor_file);
    $scraping_content = file_get_contents($scraping_infrastructure_file);
    
    $perf_has_main_class = strpos($perf_content, 'class ChatGABI_Performance_Monitor') !== false;
    $scraping_has_renamed_class = strpos($scraping_content, 'class ChatGABI_Scraping_Performance_Monitor') !== false;
    $scraping_has_old_class = strpos($scraping_content, 'class ChatGABI_Performance_Monitor') !== false;
    
    if ($perf_has_main_class && $scraping_has_renamed_class && !$scraping_has_old_class) {
        echo "<div class='success'>✅ No duplicate class declarations found</div>\n";
        echo "<div class='info'>ℹ️ Main class in performance-monitor.php: ChatGABI_Performance_Monitor</div>\n";
        echo "<div class='info'>ℹ️ Renamed class in scraping-infrastructure.php: ChatGABI_Scraping_Performance_Monitor</div>\n";
        $tests_passed++;
    } else {
        echo "<div class='error'>❌ Class declaration issues found</div>\n";
        if (!$perf_has_main_class) echo "<div class='error'>  - Main class missing from performance-monitor.php</div>\n";
        if (!$scraping_has_renamed_class) echo "<div class='error'>  - Renamed class missing from scraping-infrastructure.php</div>\n";
        if ($scraping_has_old_class) echo "<div class='error'>  - Old class name still exists in scraping-infrastructure.php</div>\n";
    }
} else {
    echo "<div class='error'>❌ Cannot check for duplicate declarations - files missing</div>\n";
}

// Test Summary
echo "<h2>Test Summary</h2>\n";
echo "<div class='info'><strong>Tests Passed: {$tests_passed} / {$total_tests}</strong></div>\n";

if ($tests_passed === $total_tests) {
    echo "<div class='success'><strong>🎉 All tests passed! Class name conflict has been resolved.</strong></div>\n";
    echo "<div class='info'>The ChatGABI site should now load without the fatal error.</div>\n";
} else {
    echo "<div class='error'><strong>❌ Some tests failed. Please review the issues above.</strong></div>\n";
}

echo "<h2>Fix Summary</h2>\n";
echo "<div class='info'><strong>Changes Made:</strong></div>\n";
echo "<div class='info'>1. Renamed ChatGABI_Performance_Monitor in scraping-infrastructure.php to ChatGABI_Scraping_Performance_Monitor</div>\n";
echo "<div class='info'>2. Updated instantiation in advanced-web-scraper.php to use the renamed class</div>\n";
echo "<div class='info'>3. Added clear documentation comments explaining the rename</div>\n";

?>
