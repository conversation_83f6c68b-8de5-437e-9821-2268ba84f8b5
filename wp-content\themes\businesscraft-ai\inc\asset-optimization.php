<?php
/**
 * Asset Loading Optimization for ChatGABI
 * 
 * Handles conditional script loading and asset optimization
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Asset Optimization Manager
 */
class ChatGABI_Asset_Optimizer {
    
    private static $instance = null;
    private $loaded_assets = array();
    private $deferred_scripts = array();
    private $critical_css = '';
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize asset optimization
     */
    public function init_asset_optimization() {
        // Initialize critical CSS
        $this->init_critical_css();

        // Set up deferred scripts
        $this->init_deferred_scripts();

        // Initialize asset monitoring
        $this->init_asset_monitoring();

        return true;
    }

    /**
     * Initialize critical CSS
     */
    private function init_critical_css() {
        $this->critical_css = $this->get_critical_css();
        return true;
    }

    /**
     * Initialize deferred scripts
     */
    private function init_deferred_scripts() {
        $this->deferred_scripts = array(
            'chart-js',
            'chatgabi-analytics',
            'chatgabi-feedback',
            'chatgabi-user-preferences',
            'chatgabi-advanced-analytics',
            'chatgabi-offline-queue'
        );
        return true;
    }

    /**
     * Initialize asset monitoring
     */
    private function init_asset_monitoring() {
        // Track loaded assets for optimization
        add_action('wp_enqueue_scripts', array($this, 'track_enqueued_assets'), 999);
        add_action('admin_enqueue_scripts', array($this, 'track_enqueued_assets'), 999);
        return true;
    }

    /**
     * Track enqueued assets
     */
    public function track_enqueued_assets() {
        global $wp_scripts, $wp_styles;

        if (isset($wp_scripts->queue)) {
            $this->loaded_assets['scripts'] = $wp_scripts->queue;
        }

        if (isset($wp_styles->queue)) {
            $this->loaded_assets['styles'] = $wp_styles->queue;
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'optimize_script_loading'), 5);
        add_action('wp_print_scripts', array($this, 'defer_non_critical_scripts'), 100);
        add_action('wp_head', array($this, 'output_critical_css'), 1);
        add_action('wp_footer', array($this, 'load_deferred_assets'), 20);
        
        // Admin optimization
        add_action('admin_enqueue_scripts', array($this, 'optimize_admin_scripts'), 5);
    }
    
    /**
     * Optimize script loading based on page context
     */
    public function optimize_script_loading() {
        // Dequeue unnecessary scripts on specific pages
        $this->conditional_script_loading();
        
        // Optimize jQuery loading
        $this->optimize_jquery_loading();
        
        // Handle critical vs non-critical assets
        $this->prioritize_critical_assets();
    }
    
    /**
     * Conditional script loading based on page context
     */
    private function conditional_script_loading() {
        global $post;
        
        // Dashboard page assets
        if (is_page('dashboard') || is_page_template('page-dashboard.php')) {
            $this->load_dashboard_assets();
        }
        
        // Templates page assets
        else if (is_page('templates') || has_shortcode(get_post()->post_content ?? '', 'chatgabi_prompt_templates')) {
            $this->load_templates_assets();
        }
        
        // Chat-specific assets
        else if (has_shortcode(get_post()->post_content ?? '', 'chatgabi_dashboard') || is_front_page()) {
            $this->load_chat_assets();
        }
        
        // Admin pages
        else if (is_admin()) {
            $this->load_admin_assets();
        }
        
        // Basic pages - minimal assets
        else {
            $this->load_minimal_assets();
        }
    }
    
    /**
     * Load dashboard-specific assets
     */
    private function load_dashboard_assets() {
        wp_enqueue_script('chart-js');
        wp_enqueue_script('chatgabi-dashboard');
        wp_enqueue_script('chatgabi-payments');
        wp_enqueue_script('chatgabi-feedback');
        wp_enqueue_script('chatgabi-response-streaming');
        
        wp_enqueue_style('chatgabi-dashboard-phase3');
        wp_enqueue_style('chatgabi-responsive-accessibility');
        
        $this->loaded_assets[] = 'dashboard';
    }
    
    /**
     * Load templates-specific assets
     */
    private function load_templates_assets() {
        wp_enqueue_script('chart-js');
        wp_enqueue_script('chatgabi-prompt-templates');
        wp_enqueue_script('chatgabi-template-preview-enhanced');
        
        wp_enqueue_style('chatgabi-prompt-templates');
        wp_enqueue_style('chatgabi-template-preview-enhanced');
        wp_enqueue_style('chatgabi-ai-template-widgets');
        
        $this->loaded_assets[] = 'templates';
    }
    
    /**
     * Load chat-specific assets
     */
    private function load_chat_assets() {
        wp_enqueue_script('chatgabi-chat-block');
        wp_enqueue_script('chatgabi-response-streaming');
        wp_enqueue_script('chatgabi-feedback');
        
        $this->loaded_assets[] = 'chat';
    }
    
    /**
     * Load admin-specific assets
     */
    private function load_admin_assets() {
        if (is_admin()) {
            // Only load on ChatGABI admin pages
            $screen = get_current_screen();
            if ($screen && strpos($screen->id, 'chatgabi') !== false) {
                wp_enqueue_script('chart-js');
                wp_enqueue_style('chatgabi-admin-styles');
            }
        }
        
        $this->loaded_assets[] = 'admin';
    }
    
    /**
     * Load minimal assets for basic pages
     */
    private function load_minimal_assets() {
        // Only essential styles
        wp_enqueue_style('chatgabi-style');
        wp_enqueue_style('chatgabi-language-switcher');
        
        $this->loaded_assets[] = 'minimal';
    }
    
    /**
     * Optimize jQuery loading
     */
    private function optimize_jquery_loading() {
        if (!is_admin() && !is_customize_preview()) {
            // Use WordPress's jQuery but optimize loading
            wp_deregister_script('jquery');
            wp_register_script('jquery', includes_url('/js/jquery/jquery.min.js'), false, null, true);
            wp_enqueue_script('jquery');
        }
    }
    
    /**
     * Prioritize critical assets
     */
    private function prioritize_critical_assets() {
        // Mark non-critical scripts for deferred loading
        $non_critical_scripts = array(
            'chart-js',
            'chatgabi-advanced-analytics',
            'chatgabi-offline-queue',
            'chatgabi-personalization',
            'chatgabi-feedback-loops'
        );
        
        foreach ($non_critical_scripts as $script) {
            if (wp_script_is($script, 'enqueued')) {
                $this->deferred_scripts[] = $script;
            }
        }
    }
    
    /**
     * Defer non-critical scripts
     */
    public function defer_non_critical_scripts() {
        global $wp_scripts;
        
        foreach ($this->deferred_scripts as $script_handle) {
            if (isset($wp_scripts->registered[$script_handle])) {
                $wp_scripts->registered[$script_handle]->extra['defer'] = true;
            }
        }
    }
    
    /**
     * Output critical CSS inline
     */
    public function output_critical_css() {
        if (!is_admin()) {
            echo '<style id="chatgabi-critical-css">';
            echo $this->get_critical_css();
            echo '</style>';
        }
    }
    
    /**
     * Get critical CSS
     */
    private function get_critical_css() {
        return '
            .chatgabi-loading{display:flex;align-items:center;justify-content:center;padding:20px}
            .chatgabi-error{background:#ffe7e7;border:1px solid #dc3232;color:#dc3232;padding:15px;margin:10px 0;border-radius:4px}
            .chatgabi-notice{background:#e7f3ff;border:1px solid #007cba;color:#007cba;padding:15px;margin:10px 0;border-radius:4px}
            .chatgabi-chat{max-width:100%;margin:0 auto}
            .chat-input{width:100%;min-height:80px;padding:12px;border:1px solid #ddd;border-radius:4px;font-family:inherit}
            .chat-submit{background:#007cba;color:white;padding:12px 24px;border:none;border-radius:4px;cursor:pointer;font-weight:500}
            .chat-submit:hover{background:#005a87}
            .chat-submit:disabled{opacity:0.6;cursor:not-allowed}
            @media (max-width:768px){.chatgabi-chat{padding:10px}.chat-input{min-height:60px}.chat-submit{width:100%;margin-top:10px}}
        ';
    }
    
    /**
     * Load deferred assets in footer
     */
    public function load_deferred_assets() {
        if (!empty($this->deferred_scripts)) {
            echo '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    setTimeout(function() {
                        // Load deferred scripts after initial page load
                        var deferredScripts = ' . json_encode($this->deferred_scripts) . ';
                        deferredScripts.forEach(function(script) {
                            if (window[script + "_deferred"]) {
                                window[script + "_deferred"]();
                            }
                        });
                    }, 1000);
                });
            </script>';
        }
    }
    
    /**
     * Optimize admin scripts
     */
    public function optimize_admin_scripts($hook) {
        // Only load ChatGABI admin assets on relevant pages
        if (strpos($hook, 'chatgabi') === false) {
            return;
        }
        
        wp_enqueue_script('chart-js');
        wp_enqueue_style('chatgabi-admin-styles');
    }
    
    /**
     * Get loaded assets for debugging
     */
    public function get_loaded_assets() {
        return $this->loaded_assets;
    }
    
    /**
     * Clear asset cache
     */
    public function clear_asset_cache() {
        delete_transient('chatgabi_asset_optimization');
        wp_cache_flush();
    }
}

// Initialize asset optimizer
add_action('init', function() {
    ChatGABI_Asset_Optimizer::get_instance();
});

// Helper functions
function chatgabi_get_loaded_assets() {
    $optimizer = ChatGABI_Asset_Optimizer::get_instance();
    return $optimizer->get_loaded_assets();
}

function chatgabi_clear_asset_cache() {
    $optimizer = ChatGABI_Asset_Optimizer::get_instance();
    return $optimizer->clear_asset_cache();
}
