<?php
/**
 * Performance Testing & Monitoring System for ChatGABI
 * 
 * Provides real-time performance monitoring, benchmarking, and alerts
 * 
 * @package ChatGABI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Performance Monitor Class
 */
class ChatGABI_Performance_Monitor {
    
    private static $instance = null;
    private $performance_data = array();
    private $benchmarks = array();
    private $thresholds = array();
    private $alerts = array();
    private $monitoring_active = true;
    
    // Performance thresholds (in milliseconds unless specified)
    const THRESHOLD_PAGE_LOAD = 2000;      // 2 seconds
    const THRESHOLD_DATABASE_QUERY = 100;  // 100ms
    const THRESHOLD_API_RESPONSE = 3000;   // 3 seconds
    const THRESHOLD_MEMORY_USAGE = 128;    // 128MB
    const THRESHOLD_CPU_USAGE = 80;        // 80%
    
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_performance_tracking();
        $this->set_performance_thresholds();
        $this->init_hooks();
        $this->init_benchmarks();
    }

    /**
     * Start monitoring
     */
    public function start_monitoring() {
        $this->monitoring_active = true;
        $this->start_request_monitoring();

        // Initialize performance data collection
        $this->performance_data['monitoring_started'] = microtime(true);
        $this->performance_data['start_memory'] = memory_get_usage(true);

        return true;
    }

    /**
     * Get response times
     */
    public function get_response_times() {
        $response_times = array();

        // Get current performance data
        $current_data = get_transient('chatgabi_current_performance');
        if ($current_data && isset($current_data['total_execution_time'])) {
            $response_times['current'] = $current_data['total_execution_time'] * 1000; // Convert to ms
        }

        // Get historical data
        $historical_data = get_option('chatgabi_performance_history', array());
        if (!empty($historical_data)) {
            $recent_times = array_slice($historical_data, -10); // Last 10 entries
            $response_times['recent'] = array_column($recent_times, 'page_load_time');
            $response_times['average'] = !empty($response_times['recent']) ?
                array_sum($response_times['recent']) / count($response_times['recent']) : 0;
        }

        return $response_times;
    }

    /**
     * Get response time score
     */
    public function get_response_time_score() {
        $response_times = $this->get_response_times();

        $score = 100;

        // Use current response time if available, otherwise use average
        $current_time = $response_times['current'] ?? $response_times['average'] ?? 0;

        // Score based on response time thresholds
        if ($current_time > self::THRESHOLD_PAGE_LOAD) {
            $score -= (($current_time - self::THRESHOLD_PAGE_LOAD) / self::THRESHOLD_PAGE_LOAD) * 50;
        }

        // Bonus for very fast response times
        if ($current_time < 1000) { // Under 1 second
            $score += (1000 - $current_time) / 1000 * 10;
        }

        return max(0, min(100, round($score, 2)));
    }

    /**
     * Log page performance
     */
    public function log_page_performance() {
        if (!$this->monitoring_active) {
            return false;
        }

        // Calculate final performance metrics
        $end_time = microtime(true);
        $start_time = $this->performance_data['monitoring_started'] ?? $this->performance_data['script_start'] ?? $end_time;
        $total_time = $end_time - $start_time;

        $performance_log = array(
            'timestamp' => time(),
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'total_execution_time' => $total_time,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'database_queries' => count($this->performance_data['database_queries'] ?? array()),
            'slow_queries' => count(array_filter($this->performance_data['database_queries'] ?? array(), function($q) {
                return $q['slow'] ?? false;
            }))
        );

        // Store current performance data
        set_transient('chatgabi_current_performance', $performance_log, 300);

        // Add to historical data
        $historical_data = get_option('chatgabi_performance_history', array());
        $historical_data[] = $performance_log;

        // Keep only last 100 entries
        if (count($historical_data) > 100) {
            $historical_data = array_slice($historical_data, -100);
        }

        update_option('chatgabi_performance_history', $historical_data);

        return true;
    }
    
    /**
     * Initialize performance tracking
     */
    private function init_performance_tracking() {
        $this->performance_data = array(
            'request_start' => $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true),
            'script_start' => microtime(true),
            'memory_start' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'database_queries' => array(),
            'api_calls' => array(),
            'cache_hits' => 0,
            'cache_misses' => 0,
            'errors' => array(),
            'slow_operations' => array(),
            'user_interactions' => array()
        );
    }
    
    /**
     * Set performance thresholds
     */
    private function set_performance_thresholds() {
        $this->thresholds = array(
            'page_load_time' => apply_filters('chatgabi_threshold_page_load', self::THRESHOLD_PAGE_LOAD),
            'database_query_time' => apply_filters('chatgabi_threshold_db_query', self::THRESHOLD_DATABASE_QUERY),
            'api_response_time' => apply_filters('chatgabi_threshold_api_response', self::THRESHOLD_API_RESPONSE),
            'memory_usage' => apply_filters('chatgabi_threshold_memory', self::THRESHOLD_MEMORY_USAGE),
            'cpu_usage' => apply_filters('chatgabi_threshold_cpu', self::THRESHOLD_CPU_USAGE),
            'error_rate' => 5, // 5% error rate threshold
            'concurrent_users' => 100, // Maximum concurrent users
            'response_size' => 2048, // 2MB response size limit
        );
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Core performance tracking hooks
        add_action('init', array($this, 'start_request_monitoring'), 1);
        add_action('wp_loaded', array($this, 'wp_loaded_checkpoint'));
        add_action('wp_head', array($this, 'wp_head_checkpoint'), 1);
        add_action('wp_footer', array($this, 'wp_footer_checkpoint'), 999);
        add_action('shutdown', array($this, 'end_request_monitoring'), 999);
        
        // Database monitoring
        add_filter('query', array($this, 'monitor_database_query'), 10, 1);
        add_action('wp_db_query_end', array($this, 'log_database_query_time'));
        
        // Cache monitoring
        add_action('wp_cache_get', array($this, 'log_cache_hit'));
        add_action('wp_cache_set', array($this, 'log_cache_set'));
        
        // Error monitoring
        add_action('wp_error_added', array($this, 'log_performance_error'), 10, 4);
        
        // AJAX monitoring
        add_action('wp_ajax_chatgabi_get_performance_data', array($this, 'ajax_get_performance_data'));
        add_action('wp_ajax_chatgabi_reset_performance_data', array($this, 'ajax_reset_performance_data'));
        add_action('wp_ajax_chatgabi_run_benchmark', array($this, 'ajax_run_benchmark'));
        
        // Admin monitoring dashboard
        if (is_admin()) {
            add_action('admin_bar_menu', array($this, 'add_admin_bar_performance_indicator'), 999);
            add_action('admin_notices', array($this, 'show_performance_alerts'));
        }
        
        // Real-time monitoring for logged-in users with capability
        if (current_user_can('manage_options')) {
            add_action('wp_enqueue_scripts', array($this, 'enqueue_monitoring_scripts'));
        }
    }
    
    /**
     * Initialize benchmarks
     */
    private function init_benchmarks() {
        $this->benchmarks = array(
            'database_operations' => array(
                'simple_select' => 'SELECT 1',
                'user_query' => 'SELECT * FROM wp_users LIMIT 1',
                'options_query' => 'SELECT option_value FROM wp_options WHERE option_name = "blogname"',
                'complex_join' => 'SELECT p.ID, p.post_title, m.meta_value FROM wp_posts p LEFT JOIN wp_postmeta m ON p.ID = m.post_id WHERE p.post_type = "post" AND p.post_status = "publish" LIMIT 10'
            ),
            'file_operations' => array(
                'read_small_file' => array('operation' => 'file_read', 'size' => 1024),
                'read_medium_file' => array('operation' => 'file_read', 'size' => 10240),
                'write_test_file' => array('operation' => 'file_write', 'size' => 1024),
                'cache_write_test' => array('operation' => 'cache_write', 'size' => 5120)
            ),
            'memory_operations' => array(
                'array_creation' => array('operation' => 'array_create', 'items' => 1000),
                'string_operations' => array('operation' => 'string_ops', 'length' => 10000),
                'object_creation' => array('operation' => 'object_create', 'count' => 100)
            )
        );
    }
    
    /**
     * Start request monitoring
     */
    public function start_request_monitoring() {
        if (!$this->monitoring_active) {
            return;
        }
        
        // Log request details
        $this->performance_data['request_uri'] = $_SERVER['REQUEST_URI'] ?? '';
        $this->performance_data['request_method'] = $_SERVER['REQUEST_METHOD'] ?? '';
        $this->performance_data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $this->performance_data['user_id'] = get_current_user_id();
        $this->performance_data['is_admin'] = is_admin();
        $this->performance_data['is_ajax'] = wp_doing_ajax();
        
        // Start output buffering to measure content size
        if (!is_admin()) {
            ob_start();
        }
    }
    
    /**
     * WordPress loaded checkpoint
     */
    public function wp_loaded_checkpoint() {
        $this->performance_data['wp_loaded_time'] = microtime(true) - $this->performance_data['script_start'];
        $this->performance_data['wp_loaded_memory'] = memory_get_usage(true);
    }
    
    /**
     * WordPress head checkpoint
     */
    public function wp_head_checkpoint() {
        $this->performance_data['wp_head_time'] = microtime(true) - $this->performance_data['script_start'];
        $this->performance_data['wp_head_memory'] = memory_get_usage(true);
    }
    
    /**
     * WordPress footer checkpoint
     */
    public function wp_footer_checkpoint() {
        $this->performance_data['wp_footer_time'] = microtime(true) - $this->performance_data['script_start'];
        $this->performance_data['wp_footer_memory'] = memory_get_usage(true);
        
        // Calculate content size
        if (!is_admin() && ob_get_level() > 0) {
            $content = ob_get_contents();
            $this->performance_data['content_size'] = strlen($content);
            $this->performance_data['content_compressed_size'] = strlen(gzcompress($content));
        }
    }
    
    /**
     * End request monitoring
     */
    public function end_request_monitoring() {
        if (!$this->monitoring_active) {
            return;
        }
        
        // Calculate final metrics
        $this->performance_data['total_execution_time'] = microtime(true) - $this->performance_data['script_start'];
        $this->performance_data['total_request_time'] = microtime(true) - $this->performance_data['request_start'];
        $this->performance_data['final_memory'] = memory_get_usage(true);
        $this->performance_data['peak_memory'] = memory_get_peak_usage(true);
        $this->performance_data['memory_used'] = $this->performance_data['final_memory'] - $this->performance_data['memory_start'];
        
        // Get database query count
        global $wpdb;
        $this->performance_data['database_query_count'] = $wpdb->num_queries;
        
        // Calculate performance scores
        $this->calculate_performance_scores();
        
        // Check for performance issues
        $this->check_performance_thresholds();
        
        // Store performance data
        $this->store_performance_data();
        
        // Generate alerts if needed
        $this->generate_performance_alerts();
        
        // Clean up output buffer
        if (!is_admin() && ob_get_level() > 0) {
            ob_end_clean();
        }
    }
    
    /**
     * Monitor database queries
     */
    public function monitor_database_query($query) {
        if (!$this->monitoring_active) {
            return $query;
        }
        
        // Start timing this query
        $this->performance_data['current_query_start'] = microtime(true);
        $this->performance_data['current_query'] = $query;
        
        return $query;
    }
    
    /**
     * Log database query completion
     */
    public function log_database_query_time() {
        if (!$this->monitoring_active || !isset($this->performance_data['current_query_start'])) {
            return;
        }
        
        $query_time = (microtime(true) - $this->performance_data['current_query_start']) * 1000; // Convert to milliseconds
        $query = $this->performance_data['current_query'];
        
        $this->performance_data['database_queries'][] = array(
            'query' => substr($query, 0, 200), // Truncate for storage
            'time' => $query_time,
            'slow' => $query_time > $this->thresholds['database_query_time']
        );
        
        // Log slow queries
        if ($query_time > $this->thresholds['database_query_time']) {
            $this->performance_data['slow_operations'][] = array(
                'type' => 'database_query',
                'time' => $query_time,
                'details' => substr($query, 0, 100),
                'timestamp' => microtime(true)
            );
        }
        
        unset($this->performance_data['current_query_start'], $this->performance_data['current_query']);
    }
    
    /**
     * Log cache hits and misses
     */
    public function log_cache_hit($key) {
        $this->performance_data['cache_hits']++;
    }
    
    public function log_cache_set($key) {
        $this->performance_data['cache_misses']++;
    }
    
    /**
     * Log performance-related errors
     */
    public function log_performance_error($code, $message, $data, $wp_error) {
        $this->performance_data['errors'][] = array(
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => microtime(true)
        );
    }
    
    /**
     * Calculate performance scores
     */
    private function calculate_performance_scores() {
        $scores = array();
        
        // Page load score (0-100, higher is better)
        $page_load_ms = $this->performance_data['total_execution_time'] * 1000;
        $scores['page_load'] = max(0, 100 - ($page_load_ms / $this->thresholds['page_load_time'] * 100));
        
        // Memory usage score
        $memory_mb = $this->performance_data['peak_memory'] / 1024 / 1024;
        $scores['memory'] = max(0, 100 - ($memory_mb / $this->thresholds['memory_usage'] * 100));
        
        // Database performance score
        $avg_query_time = 0;
        if (!empty($this->performance_data['database_queries'])) {
            $total_time = array_sum(array_column($this->performance_data['database_queries'], 'time'));
            $avg_query_time = $total_time / count($this->performance_data['database_queries']);
        }
        $scores['database'] = max(0, 100 - ($avg_query_time / $this->thresholds['database_query_time'] * 100));
        
        // Cache efficiency score
        $total_cache_ops = $this->performance_data['cache_hits'] + $this->performance_data['cache_misses'];
        $cache_hit_rate = $total_cache_ops > 0 ? ($this->performance_data['cache_hits'] / $total_cache_ops) * 100 : 100;
        $scores['cache'] = $cache_hit_rate;
        
        // Error rate score
        $error_count = count($this->performance_data['errors']);
        $scores['errors'] = max(0, 100 - ($error_count * 10)); // 10 points per error
        
        // Overall score (weighted average)
        $weights = array(
            'page_load' => 0.3,
            'memory' => 0.2,
            'database' => 0.2,
            'cache' => 0.15,
            'errors' => 0.15
        );
        
        $overall_score = 0;
        foreach ($scores as $metric => $score) {
            $overall_score += $score * $weights[$metric];
        }
        
        $this->performance_data['performance_scores'] = $scores;
        $this->performance_data['overall_score'] = round($overall_score, 1);
    }
    
    /**
     * Check performance thresholds
     */
    private function check_performance_thresholds() {
        $issues = array();
        
        // Check page load time
        $page_load_ms = $this->performance_data['total_execution_time'] * 1000;
        if ($page_load_ms > $this->thresholds['page_load_time']) {
            $issues[] = array(
                'type' => 'slow_page_load',
                'severity' => 'high',
                'value' => $page_load_ms,
                'threshold' => $this->thresholds['page_load_time'],
                'message' => sprintf('Page load time (%dms) exceeded threshold (%dms)', $page_load_ms, $this->thresholds['page_load_time'])
            );
        }
        
        // Check memory usage
        $memory_mb = $this->performance_data['peak_memory'] / 1024 / 1024;
        if ($memory_mb > $this->thresholds['memory_usage']) {
            $issues[] = array(
                'type' => 'high_memory_usage',
                'severity' => 'medium',
                'value' => $memory_mb,
                'threshold' => $this->thresholds['memory_usage'],
                'message' => sprintf('Memory usage (%.1fMB) exceeded threshold (%dMB)', $memory_mb, $this->thresholds['memory_usage'])
            );
        }
        
        // Check slow database queries
        $slow_queries = array_filter($this->performance_data['database_queries'], function($query) {
            return $query['slow'];
        });
        
        if (count($slow_queries) > 3) {
            $issues[] = array(
                'type' => 'multiple_slow_queries',
                'severity' => 'medium',
                'value' => count($slow_queries),
                'threshold' => 3,
                'message' => sprintf('%d slow database queries detected', count($slow_queries))
            );
        }
        
        // Check error count
        $error_count = count($this->performance_data['errors']);
        if ($error_count > 2) {
            $issues[] = array(
                'type' => 'multiple_errors',
                'severity' => 'high',
                'value' => $error_count,
                'threshold' => 2,
                'message' => sprintf('%d errors occurred during request', $error_count)
            );
        }
        
        // Check content size
        if (isset($this->performance_data['content_size']) && $this->performance_data['content_size'] > $this->thresholds['response_size'] * 1024) {
            $issues[] = array(
                'type' => 'large_response',
                'severity' => 'low',
                'value' => $this->performance_data['content_size'],
                'threshold' => $this->thresholds['response_size'] * 1024,
                'message' => sprintf('Response size (%.1fKB) is large', $this->performance_data['content_size'] / 1024)
            );
        }
        
        $this->performance_data['performance_issues'] = $issues;
    }
    
    /**
     * Store performance data
     */
    private function store_performance_data() {
        // Store in transient for real-time monitoring
        set_transient('chatgabi_current_performance', $this->performance_data, 300);
        
        // Store aggregated data for historical analysis
        $this->store_historical_performance_data();
        
        // Store in database for critical issues
        if (!empty($this->performance_data['performance_issues'])) {
            $critical_issues = array_filter($this->performance_data['performance_issues'], function($issue) {
                return $issue['severity'] === 'high';
            });
            
            if (!empty($critical_issues)) {
                $this->store_critical_performance_issues($critical_issues);
            }
        }
    }
    
    /**
     * Store historical performance data
     */
    private function store_historical_performance_data() {
        $historical_data = get_option('chatgabi_performance_history', array());
        
        // Keep only last 100 entries
        if (count($historical_data) >= 100) {
            $historical_data = array_slice($historical_data, -99);
        }
        
        $historical_data[] = array(
            'timestamp' => time(),
            'page_load_time' => $this->performance_data['total_execution_time'] * 1000,
            'memory_usage' => $this->performance_data['peak_memory'] / 1024 / 1024,
            'database_queries' => $this->performance_data['database_query_count'],
            'overall_score' => $this->performance_data['overall_score'],
            'request_uri' => $this->performance_data['request_uri'],
            'user_id' => $this->performance_data['user_id']
        );
        
        update_option('chatgabi_performance_history', $historical_data);
    }
    
    /**
     * Store critical performance issues
     */
    private function store_critical_performance_issues($issues) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_performance_issues';
        
        // Create table if it doesn't exist
        $this->create_performance_issues_table();
        
        foreach ($issues as $issue) {
            $wpdb->insert(
                $table_name,
                array(
                    'issue_type' => $issue['type'],
                    'severity' => $issue['severity'],
                    'value' => $issue['value'],
                    'threshold' => $issue['threshold'],
                    'message' => $issue['message'],
                    'request_uri' => $this->performance_data['request_uri'],
                    'user_id' => $this->performance_data['user_id'],
                    'performance_data' => json_encode($this->performance_data),
                    'created_at' => current_time('mysql')
                ),
                array('%s', '%s', '%f', '%f', '%s', '%s', '%d', '%s', '%s')
            );
        }
    }
    
    /**
     * Create performance issues table
     */
    private function create_performance_issues_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'chatgabi_performance_issues';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            issue_type varchar(50) NOT NULL,
            severity varchar(20) NOT NULL,
            value float NOT NULL,
            threshold float NOT NULL,
            message text NOT NULL,
            request_uri text,
            user_id bigint(20) DEFAULT 0,
            performance_data longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY issue_type (issue_type),
            KEY severity (severity),
            KEY created_at (created_at),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Generate performance alerts
     */
    private function generate_performance_alerts() {
        if (empty($this->performance_data['performance_issues'])) {
            return;
        }
        
        $critical_issues = array_filter($this->performance_data['performance_issues'], function($issue) {
            return $issue['severity'] === 'high';
        });
        
        if (!empty($critical_issues)) {
            // Send email alert for critical issues
            $this->send_performance_alert_email($critical_issues);
            
            // Store alert for admin display
            set_transient('chatgabi_performance_alert', $critical_issues, 3600);
        }
    }
    
    /**
     * Send performance alert email
     */
    private function send_performance_alert_email($issues) {
        $admin_email = get_option('admin_email');
        $subject = sprintf('[%s] Critical Performance Issues Detected', get_bloginfo('name'));
        
        $message = "Critical performance issues have been detected on your ChatGABI installation:\n\n";
        
        foreach ($issues as $issue) {
            $message .= sprintf("- %s: %s\n", strtoupper($issue['type']), $issue['message']);
        }
        
        $message .= "\nRequest Details:\n";
        $message .= sprintf("URL: %s\n", $this->performance_data['request_uri']);
        $message .= sprintf("Time: %s\n", current_time('mysql'));
        $message .= sprintf("Overall Performance Score: %.1f/100\n", $this->performance_data['overall_score']);
        
        $message .= "\nPlease check the WordPress admin dashboard for more details.";
        
        wp_mail($admin_email, $subject, $message);
    }
    
    /**
     * Add admin bar performance indicator
     */
    public function add_admin_bar_performance_indicator($wp_admin_bar) {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $performance_data = get_transient('chatgabi_current_performance');
        if (!$performance_data) {
            return;
        }
        
        $score = $performance_data['overall_score'] ?? 0;
        $color = $score >= 80 ? '#00a32a' : ($score >= 60 ? '#dba617' : '#d63638');
        
        $wp_admin_bar->add_node(array(
            'id' => 'chatgabi-performance',
            'title' => sprintf(
                '<span style="color: %s;">⚡ %.1f</span>',
                $color,
                $score
            ),
            'href' => admin_url('admin.php?page=chatgabi-performance'),
            'meta' => array(
                'title' => sprintf('ChatGABI Performance Score: %.1f/100', $score)
            )
        ));
    }
    
    /**
     * Show performance alerts in admin
     */
    public function show_performance_alerts() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        $alerts = get_transient('chatgabi_performance_alert');
        if (!$alerts) {
            return;
        }
        
        foreach ($alerts as $alert) {
            printf(
                '<div class="notice notice-error is-dismissible">
                    <p><strong>ChatGABI Performance Alert:</strong> %s</p>
                </div>',
                esc_html($alert['message'])
            );
        }
    }
    
    /**
     * Enqueue monitoring scripts
     */
    public function enqueue_monitoring_scripts() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        wp_enqueue_script(
            'chatgabi-performance-monitor',
            CHATGABI_THEME_URL . '/assets/js/performance-monitor.js',
            array('jquery'),
            CHATGABI_VERSION,
            true
        );
        
        wp_localize_script('chatgabi-performance-monitor', 'chatgabiPerformance', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgabi_performance_nonce'),
            'monitoringEnabled' => $this->monitoring_active,
            'thresholds' => $this->thresholds,
            'updateInterval' => 5000 // 5 seconds
        ));
    }
    
    /**
     * AJAX: Get performance data
     */
    public function ajax_get_performance_data() {
        check_ajax_referer('chatgabi_performance_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $current_data = get_transient('chatgabi_current_performance');
        $historical_data = get_option('chatgabi_performance_history', array());
        
        wp_send_json_success(array(
            'current' => $current_data,
            'historical' => array_slice($historical_data, -20), // Last 20 entries
            'memory_stats' => function_exists('chatgabi_get_memory_usage') ? chatgabi_get_memory_usage() : array(),
            'server_info' => $this->get_server_info()
        ));
    }
    
    /**
     * AJAX: Reset performance data
     */
    public function ajax_reset_performance_data() {
        check_ajax_referer('chatgabi_performance_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        delete_transient('chatgabi_current_performance');
        delete_option('chatgabi_performance_history');
        delete_transient('chatgabi_performance_alert');
        
        wp_send_json_success('Performance data reset successfully');
    }
    
    /**
     * AJAX: Run benchmark
     */
    public function ajax_run_benchmark() {
        check_ajax_referer('chatgabi_performance_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $benchmark_type = sanitize_text_field($_POST['benchmark_type'] ?? 'all');
        $results = $this->run_performance_benchmark($benchmark_type);
        
        wp_send_json_success($results);
    }
    
    /**
     * Run performance benchmark
     */
    public function run_performance_benchmark($type = 'all') {
        $results = array();
        
        if ($type === 'all' || $type === 'database') {
            $results['database'] = $this->benchmark_database_operations();
        }
        
        if ($type === 'all' || $type === 'file') {
            $results['file'] = $this->benchmark_file_operations();
        }
        
        if ($type === 'all' || $type === 'memory') {
            $results['memory'] = $this->benchmark_memory_operations();
        }
        
        if ($type === 'all' || $type === 'network') {
            $results['network'] = $this->benchmark_network_operations();
        }
        
        return $results;
    }
    
    /**
     * Benchmark database operations
     */
    private function benchmark_database_operations() {
        global $wpdb;
        $results = array();
        
        foreach ($this->benchmarks['database_operations'] as $name => $query) {
            $start_time = microtime(true);
            $wpdb->get_results($query);
            $end_time = microtime(true);
            
            $results[$name] = array(
                'time' => ($end_time - $start_time) * 1000, // Convert to milliseconds
                'query' => $query,
                'pass' => (($end_time - $start_time) * 1000) < $this->thresholds['database_query_time']
            );
        }
        
        return $results;
    }
    
    /**
     * Benchmark file operations
     */
    private function benchmark_file_operations() {
        $results = array();
        $upload_dir = wp_upload_dir();
        $test_file = $upload_dir['basedir'] . '/chatgabi_performance_test.txt';
        
        foreach ($this->benchmarks['file_operations'] as $name => $config) {
            $start_time = microtime(true);
            
            switch ($config['operation']) {
                case 'file_read':
                    // Create test file if it doesn't exist
                    if (!file_exists($test_file)) {
                        file_put_contents($test_file, str_repeat('A', $config['size']));
                    }
                    $content = file_get_contents($test_file);
                    break;
                    
                case 'file_write':
                    $content = str_repeat('B', $config['size']);
                    file_put_contents($test_file, $content);
                    break;
                    
                case 'cache_write':
                    $content = str_repeat('C', $config['size']);
                    set_transient('chatgabi_benchmark_test', $content, 60);
                    break;
            }
            
            $end_time = microtime(true);
            
            $results[$name] = array(
                'time' => ($end_time - $start_time) * 1000,
                'operation' => $config['operation'],
                'size' => $config['size'],
                'pass' => (($end_time - $start_time) * 1000) < 100 // 100ms threshold
            );
        }
        
        // Clean up test file
        if (file_exists($test_file)) {
            unlink($test_file);
        }
        
        return $results;
    }
    
    /**
     * Benchmark memory operations
     */
    private function benchmark_memory_operations() {
        $results = array();
        
        foreach ($this->benchmarks['memory_operations'] as $name => $config) {
            $start_time = microtime(true);
            $start_memory = memory_get_usage(true);
            
            switch ($config['operation']) {
                case 'array_create':
                    $array = array();
                    for ($i = 0; $i < $config['items']; $i++) {
                        $array[] = 'item_' . $i;
                    }
                    break;
                    
                case 'string_ops':
                    $string = str_repeat('A', $config['length']);
                    $string = strtoupper($string);
                    $string = str_replace('A', 'B', $string);
                    break;
                    
                case 'object_create':
                    $objects = array();
                    for ($i = 0; $i < $config['count']; $i++) {
                        $objects[] = new stdClass();
                        $objects[$i]->property = 'value_' . $i;
                    }
                    break;
            }
            
            $end_time = microtime(true);
            $end_memory = memory_get_usage(true);
            
            $results[$name] = array(
                'time' => ($end_time - $start_time) * 1000,
                'memory_used' => $end_memory - $start_memory,
                'operation' => $config['operation'],
                'pass' => (($end_time - $start_time) * 1000) < 50 // 50ms threshold
            );
            
            // Clean up memory
            unset($array, $string, $objects);
        }
        
        return $results;
    }
    
    /**
     * Benchmark network operations
     */
    private function benchmark_network_operations() {
        $results = array();
        
        // Test DNS resolution
        $start_time = microtime(true);
        $dns_result = gethostbyname('google.com');
        $end_time = microtime(true);
        
        $results['dns_resolution'] = array(
            'time' => ($end_time - $start_time) * 1000,
            'result' => $dns_result,
            'pass' => (($end_time - $start_time) * 1000) < 200 // 200ms threshold
        );
        
        // Test local HTTP request
        $start_time = microtime(true);
        $response = wp_remote_get(home_url(), array('timeout' => 5));
        $end_time = microtime(true);
        
        $results['local_http'] = array(
            'time' => ($end_time - $start_time) * 1000,
            'status' => is_wp_error($response) ? 'error' : wp_remote_retrieve_response_code($response),
            'pass' => !is_wp_error($response) && (($end_time - $start_time) * 1000) < 1000 // 1s threshold
        );
        
        return $results;
    }
    
    /**
     * Get server information
     */
    private function get_server_info() {
        return array(
            'php_version' => PHP_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->get_database_version(),
            'extensions' => $this->get_relevant_extensions()
        );
    }
    
    /**
     * Get database version
     */
    private function get_database_version() {
        global $wpdb;
        return $wpdb->get_var("SELECT VERSION()");
    }
    
    /**
     * Get relevant PHP extensions
     */
    private function get_relevant_extensions() {
        $extensions = array(
            'curl' => extension_loaded('curl'),
            'gd' => extension_loaded('gd'),
            'mbstring' => extension_loaded('mbstring'),
            'openssl' => extension_loaded('openssl'),
            'zip' => extension_loaded('zip'),
            'redis' => extension_loaded('redis'),
            'memcached' => extension_loaded('memcached'),
            'opcache' => extension_loaded('opcache')
        );
        
        return $extensions;
    }
    
    /**
     * Get performance statistics
     */
    public function get_performance_stats() {
        return array(
            'current_data' => get_transient('chatgabi_current_performance'),
            'historical_data' => get_option('chatgabi_performance_history', array()),
            'thresholds' => $this->thresholds,
            'monitoring_active' => $this->monitoring_active,
            'server_info' => $this->get_server_info()
        );
    }
    
    /**
     * Enable/disable monitoring
     */
    public function toggle_monitoring($enabled = null) {
        if ($enabled !== null) {
            $this->monitoring_active = (bool) $enabled;
        } else {
            $this->monitoring_active = !$this->monitoring_active;
        }
        
        update_option('chatgabi_performance_monitoring_enabled', $this->monitoring_active);
        
        return $this->monitoring_active;
    }
}

/**
 * Global helper functions
 */

/**
 * Get performance monitor instance
 */
function chatgabi_get_performance_monitor() {
    return ChatGABI_Performance_Monitor::get_instance();
}

/**
 * Get current performance data
 */
function chatgabi_get_performance_data() {
    $monitor = ChatGABI_Performance_Monitor::get_instance();
    return $monitor->get_performance_stats();
}

/**
 * Run performance benchmark
 */
function chatgabi_run_benchmark($type = 'all') {
    $monitor = ChatGABI_Performance_Monitor::get_instance();
    return $monitor->run_performance_benchmark($type);
}

/**
 * Toggle performance monitoring
 */
function chatgabi_toggle_performance_monitoring($enabled = null) {
    $monitor = ChatGABI_Performance_Monitor::get_instance();
    return $monitor->toggle_monitoring($enabled);
}

// Initialize performance monitor
add_action('init', function() {
    ChatGABI_Performance_Monitor::get_instance();
}, 2);
